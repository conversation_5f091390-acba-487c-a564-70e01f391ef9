import { Test, TestingModule } from '@nestjs/testing';
import { GeneralProcessor } from './general.processor';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EmailService } from '@/mail/email.service';
import { Job } from 'bullmq';
import { GeneralJobType } from '../queue.constants';
import type {
  GeneralTaskJobData,
  AcademicEmailReminderJobData,
  BulkDeleteUsersJobData,
} from '../queue.types';

describe('GeneralProcessor', () => {
  let processor: GeneralProcessor;
  let drizzleService: jest.Mocked<DrizzleService>;

  const mockUserId = '123e4567-e89b-12d3-a456-426614174000';
  const mockUser = {
    id: mockUserId,
    email: '<EMAIL>',
    role: 'student',
    student_profile: null,
    profile: null,
  };

  const mockDrizzleService = {
    db: {
      select: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      execute: jest.fn(),
      query: {
        users: {
          findFirst: jest.fn(),
        },
      },
      transaction: jest.fn(),
    },
  };

  const mockEmailService = {
    sendCustomEmail: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GeneralProcessor,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
      ],
    }).compile();

    processor = module.get<GeneralProcessor>(GeneralProcessor);
    drizzleService = module.get<DrizzleService>(
      DrizzleService,
    ) as jest.Mocked<DrizzleService>;
    jest.clearAllMocks();
  });

  describe('process', () => {
    it('should be defined', () => {
      expect(processor).toBeDefined();
    });

    it('should process general task job successfully', async () => {
      const job = {
        id: 'test-job-1',
        name: GeneralJobType.PROCESS_TASK,
        data: {
          taskType: 'test-task',
          payload: { key: 'value' },
        },
      } as Job<GeneralTaskJobData>;

      const result = await processor.process(job);

      expect(result).toEqual({
        success: true,
        message: 'Task test-task processed successfully',
        data: { processed: true, taskType: 'test-task' },
      });
    });

    it('should handle general task job failure', async () => {
      const job = {
        id: 'test-job-2',
        name: GeneralJobType.PROCESS_TASK,
        data: {
          taskType: 'failing-task',
          payload: { key: 'value' },
        },
      } as Job<GeneralTaskJobData>;

      // Mock a processing error by overriding the private method behavior
      const processTaskSpy = jest.spyOn(processor as any, 'processTask');
      processTaskSpy.mockImplementation(() => {
        throw new Error('Task failed');
      });

      await expect(processor.process(job)).rejects.toThrow('Task failed');

      processTaskSpy.mockRestore();
    });

    it('should process academic email reminder job successfully', async () => {
      const job = {
        id: 'test-job-3',
        name: GeneralJobType.ACADEMIC_EMAIL_REMINDER,
        data: {
          rejectionDays: 7,
          batchSize: 50,
        },
      } as Job<AcademicEmailReminderJobData>;

      const mockUsers = [
        { id: 'user-1', email: '<EMAIL>', created_at: new Date() },
        { id: 'user-2', email: '<EMAIL>', created_at: new Date() },
      ];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockUsers),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      mockEmailService.sendCustomEmail.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.data.count).toBe(2);
      expect(mockEmailService.sendCustomEmail).toHaveBeenCalledTimes(2);
    });

    it('should handle academic email reminder with no users', async () => {
      const job = {
        id: 'test-job-4',
        name: GeneralJobType.ACADEMIC_EMAIL_REMINDER,
        data: {
          rejectionDays: 7,
          batchSize: 50,
        },
      } as Job<AcademicEmailReminderJobData>;

      const mockEmptyQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValue(mockEmptyQuery);

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.data.count).toBe(0);
      expect(mockEmailService.sendCustomEmail).not.toHaveBeenCalled();
    });

    it('should process bulk delete users job successfully', async () => {
      const job = {
        id: 'test-job-5',
        name: GeneralJobType.BULK_DELETE_USERS,
        data: {
          userIds: ['user-1', 'user-2', 'user-3'],
          adminUser: {
            id: 'admin-1',
            email: '<EMAIL>',
            role: 'admin',
            firstName: 'Admin',
            lastName: 'User',
          },
        },
        updateProgress: jest.fn(),
      } as Job<BulkDeleteUsersJobData>;

      // Mock the deleteUserByIdWithRetry method to return successful deletion
      jest
        .spyOn(processor as any, 'deleteUserByIdWithRetry')
        .mockResolvedValue({
          id: 'user-1',
          email: '<EMAIL>',
          role: 'student',
          cleanupStats: { questionsOrphaned: 0, questionsReassigned: 0 },
        });

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.data.successCount).toBe(3);
      expect(result.data.failureCount).toBe(0);
    });

    it('should handle bulk delete users with partial failures', async () => {
      const job = {
        id: 'test-job-6',
        name: GeneralJobType.BULK_DELETE_USERS,
        data: {
          userIds: ['user-1', 'user-2', 'user-3'],
          adminUser: {
            id: 'admin-1',
            email: '<EMAIL>',
            role: 'admin',
          },
        },
        updateProgress: jest.fn(),
      } as Job<BulkDeleteUsersJobData>;

      // Mock deleteUserByIdWithRetry with some failures
      jest
        .spyOn(processor as any, 'deleteUserByIdWithRetry')
        .mockResolvedValueOnce({
          id: 'user-1',
          email: '<EMAIL>',
          role: 'student',
        })
        .mockRejectedValueOnce(new Error('Delete failed'))
        .mockResolvedValueOnce({
          id: 'user-3',
          email: '<EMAIL>',
          role: 'student',
        });

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.data.successCount).toBe(2);
      expect(result.data.failureCount).toBe(1);
      expect(result.data.failures).toHaveLength(1);
    });

    it('should throw error for unknown job type', async () => {
      const job = {
        id: 'test-job-7',
        name: 'UNKNOWN_TYPE',
        data: {},
      } as Job;

      await expect(processor.process(job)).rejects.toThrow(
        'Unknown job type: UNKNOWN_TYPE',
      );
    });

    it('should handle job processing errors', async () => {
      const job = {
        id: 'test-job-8',
        name: GeneralJobType.PROCESS_TASK,
        data: {
          taskType: 'error-task',
          payload: {},
        },
      } as Job<GeneralTaskJobData>;

      // Mock a processing error
      jest.spyOn(processor as any, 'processTask').mockImplementation(() => {
        throw new Error('Processing error');
      });

      await expect(processor.process(job)).rejects.toThrow('Processing error');
    });
  });

  describe('Retry Mechanism', () => {
    beforeEach(async () => {
      const mockDrizzleServiceForRetry = {
        db: {
          query: {
            users: {
              findFirst: jest.fn().mockResolvedValue(mockUser),
            },
          },
          execute: jest.fn().mockResolvedValue({
            rows: [
              {
                constraint_name: 'questions_created_by_users_id_fk',
                constraint_type: 'FOREIGN KEY',
                delete_rule: 'SET NULL',
                update_rule: 'NO ACTION',
              },
            ],
          }),
          transaction: jest.fn(),
        },
      };

      const mockEmailServiceForRetry = {
        sendCustomEmail: jest.fn().mockResolvedValue(true),
      };

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          GeneralProcessor,
          {
            provide: DrizzleService,
            useValue: mockDrizzleServiceForRetry,
          },
          {
            provide: EmailService,
            useValue: mockEmailServiceForRetry,
          },
        ],
      }).compile();

      processor = module.get<GeneralProcessor>(GeneralProcessor);
      drizzleService = module.get(DrizzleService);
    });

    describe('isQueryTimeoutError', () => {
      it('should detect query read timeout errors', () => {
        const timeoutError = new Error('Query read timeout');
        expect(processor['isQueryTimeoutError'](timeoutError)).toBe(true);
      });

      it('should detect general query timeout errors', () => {
        const timeoutError = new Error('Database query timeout occurred');
        expect(processor['isQueryTimeoutError'](timeoutError)).toBe(true);
      });

      it('should not detect non-timeout errors', () => {
        const regularError = new Error('User not found');
        expect(processor['isQueryTimeoutError'](regularError)).toBe(false);
      });

      it('should handle case insensitive timeout detection', () => {
        const timeoutError = new Error('QUERY READ TIMEOUT');
        expect(processor['isQueryTimeoutError'](timeoutError)).toBe(true);
      });

      it('should handle errors without message', () => {
        const errorWithoutMessage = new Error();
        expect(processor['isQueryTimeoutError'](errorWithoutMessage)).toBe(
          false,
        );
      });
    });

    describe('deleteUserByIdWithRetry', () => {
      beforeEach(() => {
        // Mock successful transaction by default
        drizzleService.db.transaction = jest
          .fn()
          .mockImplementation(async (callback) => {
            return await callback({
              select: jest.fn().mockReturnValue({
                from: jest.fn().mockReturnValue({
                  where: jest.fn().mockResolvedValue([]), // Empty arrays for cleanup stats
                }),
              }),
              delete: jest.fn().mockReturnValue({
                where: jest.fn().mockReturnValue({
                  returning: jest.fn().mockResolvedValue([
                    {
                      id: mockUserId,
                      email: '<EMAIL>',
                      role: 'student',
                    },
                  ]),
                }),
              }),
            });
          });
      });

      it('should succeed on first attempt when no timeout occurs', async () => {
        const result = await processor['deleteUserByIdWithRetry'](mockUserId);

        expect(result).toBeDefined();
        expect(result?.id).toBe(mockUserId);
        expect(result?.email).toBe('<EMAIL>');
      });

      it('should retry once on query timeout and succeed', async () => {
        let callCount = 0;
        const originalTransaction = drizzleService.db.transaction;

        drizzleService.db.transaction = jest
          .fn()
          .mockImplementation(async (callback) => {
            callCount++;
            if (callCount === 1) {
              throw new Error('Query read timeout');
            }
            return originalTransaction(callback);
          });

        const result = await processor['deleteUserByIdWithRetry'](mockUserId);

        expect(result).toBeDefined();
        expect(result?.id).toBe(mockUserId);
        expect(callCount).toBe(2); // First attempt failed, second succeeded
      });

      it('should fail after retry when timeout persists', async () => {
        drizzleService.db.transaction = jest
          .fn()
          .mockRejectedValue(new Error('Query read timeout'));

        await expect(
          processor['deleteUserByIdWithRetry'](mockUserId),
        ).rejects.toThrow('Query read timeout');
      });

      it('should not retry non-timeout errors', async () => {
        let callCount = 0;
        drizzleService.db.transaction = jest
          .fn()
          .mockImplementation(async () => {
            callCount++;
            throw new Error('User not found');
          });

        await expect(
          processor['deleteUserByIdWithRetry'](mockUserId),
        ).rejects.toThrow('User not found');
        expect(callCount).toBe(1); // Should not retry
      });

      it('should wait 1 second before retry', async () => {
        const startTime = Date.now();
        let callCount = 0;

        drizzleService.db.transaction = jest
          .fn()
          .mockImplementation(async (callback) => {
            callCount++;
            if (callCount === 1) {
              throw new Error('Query read timeout');
            }
            return await callback({
              select: jest.fn().mockReturnValue({
                from: jest.fn().mockReturnValue({
                  where: jest.fn().mockResolvedValue([]),
                }),
              }),
              delete: jest.fn().mockReturnValue({
                where: jest.fn().mockReturnValue({
                  returning: jest.fn().mockResolvedValue([
                    {
                      id: mockUserId,
                      email: '<EMAIL>',
                      role: 'student',
                    },
                  ]),
                }),
              }),
            });
          });

        await processor['deleteUserByIdWithRetry'](mockUserId);

        const endTime = Date.now();
        const duration = endTime - startTime;

        // Should take at least 1000ms due to the retry delay
        expect(duration).toBeGreaterThanOrEqual(1000);
        expect(callCount).toBe(2);
      });
    });
  });

  describe('findRandomAdminUser', () => {
    it('should find and return a random admin user', async () => {
      const mockAdminUsers = [
        { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
        { id: 'admin-2', email: '<EMAIL>', role: 'super_admin' },
        { id: 'admin-3', email: '<EMAIL>', role: 'student_admin' },
      ];

      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue(mockAdminUsers),
        }),
      });

      const result = await (processor as any).findRandomAdminUser();

      // Should return one of the admin users
      expect(result).toBeDefined();
      expect(['admin-1', 'admin-2', 'admin-3']).toContain(result.id);
      expect(['admin', 'super_admin', 'student_admin']).toContain(result.role);
    });

    it('should return null when no admin users are found', async () => {
      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([]),
        }),
      });

      const result = await (processor as any).findRandomAdminUser();

      expect(result).toBeNull();
    });

    it('should handle database errors gracefully', async () => {
      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockRejectedValue(new Error('Database error')),
        }),
      });

      const result = await (processor as any).findRandomAdminUser();

      expect(result).toBeNull();
    });
  });

  describe('checkConstraintStatus', () => {
    it('should return migration applied when SET NULL constraint exists', async () => {
      const mockConstraintResult = {
        rows: [
          {
            constraint_name: 'questions_created_by_users_id_fk',
            constraint_type: 'FOREIGN KEY',
            delete_rule: 'SET NULL',
            update_rule: 'NO ACTION',
          },
        ],
      };

      mockDrizzleService.db.execute.mockResolvedValue(mockConstraintResult);

      const result = await (processor as any).checkConstraintStatus();

      expect(result).toEqual({
        migrationApplied: true,
        questionsConstraintType: 'SET NULL',
        requiresManualCleanup: false,
      });
    });

    it('should return migration not applied when constraint is missing', async () => {
      const mockConstraintResult = { rows: [] };

      mockDrizzleService.db.execute.mockResolvedValue(mockConstraintResult);

      const result = await (processor as any).checkConstraintStatus();

      expect(result).toEqual({
        migrationApplied: false,
        questionsConstraintType: 'MISSING',
        requiresManualCleanup: true,
      });
    });

    it('should return migration not applied when constraint is RESTRICT', async () => {
      const mockConstraintResult = {
        rows: [
          {
            constraint_name: 'questions_created_by_users_id_fk',
            constraint_type: 'FOREIGN KEY',
            delete_rule: 'RESTRICT',
            update_rule: 'NO ACTION',
          },
        ],
      };

      mockDrizzleService.db.execute.mockResolvedValue(mockConstraintResult);

      const result = await (processor as any).checkConstraintStatus();

      expect(result).toEqual({
        migrationApplied: false,
        questionsConstraintType: 'RESTRICT',
        requiresManualCleanup: true,
      });
    });

    it('should handle database errors gracefully', async () => {
      mockDrizzleService.db.execute.mockRejectedValue(
        new Error('Database error'),
      );

      const result = await (processor as any).checkConstraintStatus();

      expect(result).toEqual({
        migrationApplied: false,
        questionsConstraintType: 'ERROR',
        requiresManualCleanup: true,
      });
    });
  });

  describe('deleteUserById', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      role: 'student',
      student_profile: null,
      profile: null,
    };

    beforeEach(() => {
      mockDrizzleService.db.query.users.findFirst.mockResolvedValue(mockUser);
    });

    it('should throw error when user not found', async () => {
      mockDrizzleService.db.query.users.findFirst.mockResolvedValue(null);

      await expect(
        (processor as any).deleteUserById('nonexistent'),
      ).rejects.toThrow('User with ID nonexistent not found');
    });

    it('should use manual cleanup when migration not applied', async () => {
      jest.spyOn(processor as any, 'checkConstraintStatus').mockResolvedValue({
        migrationApplied: false,
        questionsConstraintType: 'RESTRICT',
        requiresManualCleanup: true,
      });

      const mockResult = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'student',
        cleanupStats: { questionsOrphaned: 0, questionsReassigned: 0 },
      };

      jest
        .spyOn(processor as any, 'deleteUserWithManualCleanup')
        .mockResolvedValue(mockResult);

      const result = await (processor as any).deleteUserById('user-123');

      expect(result).toEqual(mockResult);
      expect((processor as any).checkConstraintStatus).toHaveBeenCalled();
      expect(
        (processor as any).deleteUserWithManualCleanup,
      ).toHaveBeenCalledWith('user-123', mockUser);
    });

    it('should use direct deletion when migration applied', async () => {
      jest.spyOn(processor as any, 'checkConstraintStatus').mockResolvedValue({
        migrationApplied: true,
        questionsConstraintType: 'SET NULL',
        requiresManualCleanup: false,
      });

      const mockResult = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'student',
        cleanupStats: { questionsOrphaned: 0, questionsReassigned: 0 },
      };

      jest
        .spyOn(processor as any, 'deleteUserDirect')
        .mockResolvedValue(mockResult);

      const result = await (processor as any).deleteUserById('user-123');

      expect(result).toEqual(mockResult);
      expect((processor as any).checkConstraintStatus).toHaveBeenCalled();
      expect((processor as any).deleteUserDirect).toHaveBeenCalledWith(
        'user-123',
        mockUser,
      );
    });
  });
});
