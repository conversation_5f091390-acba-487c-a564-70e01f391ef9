import { Test, TestingModule } from '@nestjs/testing';
import { Job } from 'bullmq';
import { InAppProcessor } from './in-app.processor';
import { EnhancedNotificationService } from '../../enhanced-notification/enhanced-notification.service';
import { InAppJobType } from '../queue.constants';
import type { SingleInAppJobData, BulkInAppJobData } from '../queue.types';
import { Subject } from 'rxjs';

describe('InAppProcessor', () => {
  let processor: InAppProcessor;
  let mockNotificationService: jest.Mocked<EnhancedNotificationService>;
  let mockSubject: jest.Mocked<Subject<any>>;

  beforeEach(async () => {
    // Create mock subject
    mockSubject = {
      next: jest.fn(),
      error: jest.fn(),
      complete: jest.fn(),
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
    } as any;

    // Create mock notification service
    mockNotificationService = {
      getInAppNotificationSubject: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InAppProcessor,
        {
          provide: EnhancedNotificationService,
          useValue: mockNotificationService,
        },
      ],
    }).compile();

    processor = module.get<InAppProcessor>(InAppProcessor);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('process', () => {
    it('should be defined', () => {
      expect(processor).toBeDefined();
    });

    it('should process single in-app notification job successfully', async () => {
      const job = {
        id: 'test-job-1',
        name: InAppJobType.SEND_SINGLE,
        data: {
          userId: 'user-123',
          title: 'Test Notification',
          body: 'This is a test notification',
          data: { key: 'value' },
          module: 'quiz',
        },
      } as Job<SingleInAppJobData>;

      mockNotificationService.getInAppNotificationSubject.mockReturnValue(
        mockSubject,
      );

      const result = await processor.process(job);

      expect(
        mockNotificationService.getInAppNotificationSubject,
      ).toHaveBeenCalledWith('quiz');
      expect(mockSubject.next).toHaveBeenCalledWith({
        userId: 'user-123',
        title: 'Test Notification',
        body: 'This is a test notification',
        data: { key: 'value' },
        timestamp: expect.any(Date),
      });

      expect(result).toEqual({
        success: true,
        message: 'In-app notification sent to user user-123',
        data: {
          summary: 'User: user-123..., Module: quiz',
          title: 'Test Notification',
          body: 'This is a test notification',
        },
      });
    });

    it('should handle single in-app notification with empty data', async () => {
      const job = {
        id: 'test-job-2',
        name: InAppJobType.SEND_SINGLE,
        data: {
          userId: 'user-456',
          title: 'Test Notification',
          body: 'This is a test notification',
          module: 'post',
        },
      } as Job<SingleInAppJobData>;

      mockNotificationService.getInAppNotificationSubject.mockReturnValue(
        mockSubject,
      );

      const result = await processor.process(job);

      expect(mockSubject.next).toHaveBeenCalledWith({
        userId: 'user-456',
        title: 'Test Notification',
        body: 'This is a test notification',
        data: {},
        timestamp: expect.any(Date),
      });

      expect(result.success).toBe(true);
    });

    it('should handle single in-app notification when no subject found', async () => {
      const job = {
        id: 'test-job-3',
        name: InAppJobType.SEND_SINGLE,
        data: {
          userId: 'user-789',
          title: 'Test Notification',
          body: 'This is a test notification',
          module: 'unknown-module',
        },
      } as Job<SingleInAppJobData>;

      mockNotificationService.getInAppNotificationSubject.mockReturnValue(null);

      const result = await processor.process(job);

      expect(
        mockNotificationService.getInAppNotificationSubject,
      ).toHaveBeenCalledWith('unknown-module');
      expect(mockSubject.next).not.toHaveBeenCalled();

      expect(result).toEqual({
        success: false,
        message: 'No notification subject found for event type unknown-module',
      });
    });

    it('should handle single in-app notification when subject throws error', async () => {
      const job = {
        id: 'test-job-4',
        name: InAppJobType.SEND_SINGLE,
        data: {
          userId: 'user-error',
          title: 'Test Notification',
          body: 'This is a test notification',
          module: 'quiz',
        },
      } as Job<SingleInAppJobData>;

      const errorSubject = {
        next: jest.fn().mockImplementation(() => {
          throw new Error('Subject error');
        }),
      };

      mockNotificationService.getInAppNotificationSubject.mockReturnValue(
        errorSubject as any,
      );

      const result = await processor.process(job);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send in-app notification to user user-error',
        error: 'Subject error',
        data: {
          userId: 'user-error',
          module: 'quiz',
        },
      });
    });

    it('should process bulk in-app notification job successfully', async () => {
      const job = {
        id: 'test-job-5',
        name: InAppJobType.SEND_BULK,
        data: {
          userIds: ['user-1', 'user-2', 'user-3'],
          title: 'Bulk Notification',
          body: 'This is a bulk notification',
          data: { type: 'announcement' },
          module: 'general',
        },
      } as Job<BulkInAppJobData>;

      mockNotificationService.getInAppNotificationSubject.mockReturnValue(
        mockSubject,
      );

      const result = await processor.process(job);

      expect(
        mockNotificationService.getInAppNotificationSubject,
      ).toHaveBeenCalledWith('general');
      expect(mockSubject.next).toHaveBeenCalledTimes(3);

      expect(mockSubject.next).toHaveBeenNthCalledWith(1, {
        userId: 'user-1',
        title: 'Bulk Notification',
        body: 'This is a bulk notification',
        data: { type: 'announcement' },
        timestamp: expect.any(Date),
      });

      expect(result).toEqual({
        success: true,
        message: 'Bulk in-app notifications sent to 3/3 users (0 failed)',
        data: {
          summary: 'Users: 3, Module: general, Success: 3, Failure: 0',
          title: 'Bulk Notification',
          body: 'This is a bulk notification',
        },
      });
    });

    it('should handle bulk in-app notification with empty data', async () => {
      const job = {
        id: 'test-job-6',
        name: InAppJobType.SEND_BULK,
        data: {
          userIds: ['user-1', 'user-2'],
          title: 'Bulk Notification',
          body: 'This is a bulk notification',
          module: 'general',
        },
      } as Job<BulkInAppJobData>;

      mockNotificationService.getInAppNotificationSubject.mockReturnValue(
        mockSubject,
      );

      const result = await processor.process(job);

      expect(mockSubject.next).toHaveBeenCalledWith(
        expect.objectContaining({
          data: {},
        }),
      );

      expect(result.success).toBe(true);
    });

    it('should handle bulk in-app notification when no subject found', async () => {
      const job = {
        id: 'test-job-7',
        name: InAppJobType.SEND_BULK,
        data: {
          userIds: ['user-1', 'user-2'],
          title: 'Bulk Notification',
          body: 'This is a bulk notification',
          module: 'unknown-module',
        },
      } as Job<BulkInAppJobData>;

      mockNotificationService.getInAppNotificationSubject.mockReturnValue(null);

      const result = await processor.process(job);

      expect(
        mockNotificationService.getInAppNotificationSubject,
      ).toHaveBeenCalledWith('unknown-module');
      expect(mockSubject.next).not.toHaveBeenCalled();

      expect(result).toEqual({
        success: false,
        message: 'No notification subject found for event type unknown-module',
      });
    });

    it('should handle bulk in-app notification with partial failures', async () => {
      const job = {
        id: 'test-job-8',
        name: InAppJobType.SEND_BULK,
        data: {
          userIds: ['user-1', 'user-2', 'user-3'],
          title: 'Bulk Notification',
          body: 'This is a bulk notification',
          module: 'general',
        },
      } as Job<BulkInAppJobData>;

      const partialErrorSubject = {
        next: jest
          .fn()
          .mockImplementationOnce(() => {}) // Success for user-1
          .mockImplementationOnce(() => {
            throw new Error('Failed for user-2');
          }) // Failure for user-2
          .mockImplementationOnce(() => {}), // Success for user-3
      };

      mockNotificationService.getInAppNotificationSubject.mockReturnValue(
        partialErrorSubject as any,
      );

      const result = await processor.process(job);

      expect(result).toEqual({
        success: true,
        message: 'Bulk in-app notifications sent to 2/3 users (1 failed)',
        data: {
          summary: 'Users: 3, Module: general, Success: 2, Failure: 1',
          title: 'Bulk Notification',
          body: 'This is a bulk notification',
        },
      });
    });

    it('should handle bulk in-app notification with complete failure', async () => {
      const job = {
        id: 'test-job-9',
        name: InAppJobType.SEND_BULK,
        data: {
          userIds: ['user-1', 'user-2'],
          title: 'Bulk Notification',
          body: 'This is a bulk notification',
          module: 'general',
        },
      } as Job<BulkInAppJobData>;

      mockNotificationService.getInAppNotificationSubject.mockImplementation(
        () => {
          throw new Error('Service error');
        },
      );

      const result = await processor.process(job);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send bulk in-app notifications',
        error: 'Service error',
        data: {
          userCount: 2,
          module: 'general',
        },
      });
    });

    it('should throw error for unknown job type', async () => {
      const job = {
        id: 'test-job-10',
        name: 'UNKNOWN_TYPE',
        data: {},
      } as Job;

      await expect(processor.process(job)).rejects.toThrow(
        'Unknown job type: UNKNOWN_TYPE',
      );
    });

    it('should handle empty user list in bulk notification', async () => {
      const job = {
        id: 'test-job-11',
        name: InAppJobType.SEND_BULK,
        data: {
          userIds: [],
          title: 'Bulk Notification',
          body: 'This is a bulk notification',
          module: 'general',
        },
      } as Job<BulkInAppJobData>;

      mockNotificationService.getInAppNotificationSubject.mockReturnValue(
        mockSubject,
      );

      const result = await processor.process(job);

      expect(mockSubject.next).not.toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        message: 'Bulk in-app notifications sent to 0/0 users (0 failed)',
        data: {
          summary: 'Users: 0, Module: general, Success: 0, Failure: 0',
          title: 'Bulk Notification',
          body: 'This is a bulk notification',
        },
      });
    });
  });
});
