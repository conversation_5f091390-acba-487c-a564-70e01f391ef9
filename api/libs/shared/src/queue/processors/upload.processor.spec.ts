import { Test, TestingModule } from '@nestjs/testing';
import { Job } from 'bullmq';
import { UploadProcessor } from './upload.processor';
import { UploadService } from '@/upload/upload.service';
import { UploadQueueService } from '@app/shared/upload/upload.service';
import { UploadJobType } from '../queue.constants';
import type { UploadJobData } from '../queue.types';
import { post_statuses } from '@/db/schema';

describe('UploadProcessor', () => {
  let processor: UploadProcessor;
  let mockUploadService: jest.Mocked<UploadService>;
  let mockUploadQueueService: jest.Mocked<UploadQueueService>;

  beforeEach(async () => {
    // Create mock services
    mockUploadService = {
      uploadFileToS3: jest.fn(),
    } as any;

    mockUploadQueueService = {
      updatePostImages: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UploadProcessor,
        {
          provide: UploadService,
          useValue: mockUploadService,
        },
        {
          provide: UploadQueueService,
          useValue: mockUploadQueueService,
        },
      ],
    }).compile();

    processor = module.get<UploadProcessor>(UploadProcessor);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('process', () => {
    it('should be defined', () => {
      expect(processor).toBeDefined();
    });

    it('should process upload file job successfully', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test-image.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        buffer: Buffer.from('fake-image-data'),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const job = {
        id: 'test-job-1',
        name: UploadJobType.UPLOAD_FILE,
        data: {
          file: mockFile,
          postId: 'post-123',
          isLastImage: false,
          status: post_statuses.ACTIVE,
        },
      } as Job<UploadJobData>;

      const mockUploadResult = {
        imageUrl: 'https://s3.amazonaws.com/bucket/test-image.jpg',
      };

      mockUploadService.uploadFileToS3.mockResolvedValue(mockUploadResult);
      mockUploadQueueService.updatePostImages.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(mockUploadService.uploadFileToS3).toHaveBeenCalledWith(mockFile);
      expect(mockUploadQueueService.updatePostImages).toHaveBeenCalledWith({
        imageUrl: 'https://s3.amazonaws.com/bucket/test-image.jpg',
        postId: 'post-123',
        isLastImage: false,
        status: post_statuses.ACTIVE,
      });

      expect(result).toEqual({
        success: true,
        message: 'Successfully uploaded file test-image.jpg for post post-123',
        data: { processed: true },
      });
    });

    it('should process upload file job with last image flag', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'final-image.png',
        encoding: '7bit',
        mimetype: 'image/png',
        size: 2048,
        buffer: Buffer.from('fake-image-data'),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const job = {
        id: 'test-job-2',
        name: UploadJobType.UPLOAD_FILE,
        data: {
          file: mockFile,
          postId: 'post-456',
          isLastImage: true,
          status: post_statuses.ACTIVE,
        },
      } as Job<UploadJobData>;

      const mockUploadResult = {
        imageUrl: 'https://s3.amazonaws.com/bucket/final-image.png',
      };

      mockUploadService.uploadFileToS3.mockResolvedValue(mockUploadResult);
      mockUploadQueueService.updatePostImages.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(mockUploadQueueService.updatePostImages).toHaveBeenCalledWith({
        imageUrl: 'https://s3.amazonaws.com/bucket/final-image.png',
        postId: 'post-456',
        isLastImage: true,
        status: post_statuses.ACTIVE,
      });

      expect(result.success).toBe(true);
    });

    it('should process upload file job without optional fields', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'simple-image.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 512,
        buffer: Buffer.from('fake-image-data'),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const job = {
        id: 'test-job-3',
        name: UploadJobType.UPLOAD_FILE,
        data: {
          file: mockFile,
          postId: 'post-789',
          // No isLastImage or status provided
        },
      } as Job<UploadJobData>;

      const mockUploadResult = {
        imageUrl: 'https://s3.amazonaws.com/bucket/simple-image.jpg',
      };

      mockUploadService.uploadFileToS3.mockResolvedValue(mockUploadResult);
      mockUploadQueueService.updatePostImages.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(mockUploadQueueService.updatePostImages).toHaveBeenCalledWith({
        imageUrl: 'https://s3.amazonaws.com/bucket/simple-image.jpg',
        postId: 'post-789',
        isLastImage: undefined,
        status: undefined,
      });

      expect(result.success).toBe(true);
    });

    it('should handle upload service failure', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'failed-upload.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        buffer: Buffer.from('fake-image-data'),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const job = {
        id: 'test-job-4',
        name: UploadJobType.UPLOAD_FILE,
        data: {
          file: mockFile,
          postId: 'post-error',
        },
      } as Job<UploadJobData>;

      mockUploadService.uploadFileToS3.mockRejectedValue(
        new Error('S3 upload failed'),
      );

      const result = await processor.process(job);

      expect(mockUploadService.uploadFileToS3).toHaveBeenCalledWith(mockFile);
      expect(mockUploadQueueService.updatePostImages).not.toHaveBeenCalled();

      expect(result).toEqual({
        success: false,
        message: 'Failed to upload file failed-upload.jpg for post post-error',
        error: 'S3 upload failed',
      });
    });

    it('should handle queue service failure', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'queue-error.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        buffer: Buffer.from('fake-image-data'),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const job = {
        id: 'test-job-5',
        name: UploadJobType.UPLOAD_FILE,
        data: {
          file: mockFile,
          postId: 'post-queue-error',
        },
      } as Job<UploadJobData>;

      const mockUploadResult = {
        imageUrl: 'https://s3.amazonaws.com/bucket/queue-error.jpg',
      };

      mockUploadService.uploadFileToS3.mockResolvedValue(mockUploadResult);
      mockUploadQueueService.updatePostImages.mockRejectedValue(
        new Error('Database update failed'),
      );

      const result = await processor.process(job);

      expect(mockUploadService.uploadFileToS3).toHaveBeenCalledWith(mockFile);
      expect(mockUploadQueueService.updatePostImages).toHaveBeenCalled();

      expect(result).toEqual({
        success: false,
        message:
          'Failed to upload file queue-error.jpg for post post-queue-error',
        error: 'Database update failed',
      });
    });

    it('should handle missing imageUrl from upload service', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'no-url.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        buffer: Buffer.from('fake-image-data'),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const job = {
        id: 'test-job-6',
        name: UploadJobType.UPLOAD_FILE,
        data: {
          file: mockFile,
          postId: 'post-no-url',
        },
      } as Job<UploadJobData>;

      // Upload service returns undefined imageUrl
      const mockUploadResult = {
        imageUrl: undefined,
      };

      mockUploadService.uploadFileToS3.mockResolvedValue(mockUploadResult);
      mockUploadQueueService.updatePostImages.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(mockUploadQueueService.updatePostImages).toHaveBeenCalledWith({
        imageUrl: undefined,
        postId: 'post-no-url',
        isLastImage: undefined,
        status: undefined,
      });

      expect(result.success).toBe(true);
    });

    it('should throw error for unknown job type', async () => {
      const job = {
        id: 'test-job-7',
        name: 'UNKNOWN_TYPE',
        data: {},
      } as Job;

      await expect(processor.process(job)).rejects.toThrow(
        'Unknown job type: UNKNOWN_TYPE',
      );
    });

    it('should handle large file upload', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'large-image.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 5 * 1024 * 1024, // 5MB
        buffer: Buffer.alloc(5 * 1024 * 1024),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const job = {
        id: 'test-job-8',
        name: UploadJobType.UPLOAD_FILE,
        data: {
          file: mockFile,
          postId: 'post-large',
          isLastImage: true,
          status: post_statuses.ACTIVE,
        },
      } as Job<UploadJobData>;

      const mockUploadResult = {
        imageUrl: 'https://s3.amazonaws.com/bucket/large-image.jpg',
      };

      mockUploadService.uploadFileToS3.mockResolvedValue(mockUploadResult);
      mockUploadQueueService.updatePostImages.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.message).toContain('large-image.jpg');
    });
  });
});
