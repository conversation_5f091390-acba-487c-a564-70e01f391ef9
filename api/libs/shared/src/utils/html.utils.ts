/**
 * HTML utility functions for cleaning and processing HTML content
 * Uses html-entities for comprehensive entity decoding and html-to-text for HTML-to-text conversion
 */

import { compile } from 'html-to-text';
import { decode } from 'html-entities';

const compiledConverter = compile({
  wordwrap: false,
  preserveNewlines: false,
  decodeEntities: true,
  selectors: [
    { selector: '*', format: 'inline' },
    { selector: 'img', format: 'skip' },
    { selector: 'script', format: 'skip' },
    { selector: 'style', format: 'skip' },
  ],
});

/**
 * Clean HTML content by converting it to plain text
 * Uses html-entities for comprehensive entity decoding and html-to-text for HTML parsing
 * @param htmlContent - HTML content to clean (from rich text editor)
 * @returns Plain text content suitable for notifications
 */
export function cleanHtmlContent(htmlContent: string): string {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return htmlContent;
  }
  const decodedContent = decode(htmlContent);

  return compiledConverter(decodedContent).trim();
}

/**
 * Clean notification body content for rich text editor output
 * This handles both string content and objects with HTML fields
 * @param body - Notification body content (string with HTML) or object with HTML fields
 * @returns Cleaned plain text (string) or object with cleaned HTML fields
 */
export function cleanNotificationBody<
  T extends string | Record<string, unknown>,
>(body: T): T {
  if (!body) {
    return body;
  }

  if (typeof body === 'string') {
    const cleanedText = cleanHtmlContent(body);

    return cleanedText
      .replace(/^•\s*/, '')
      .replace(/\s{2,}/g, ' ')
      .trim() as T;
  }

  if (typeof body === 'object' && body !== null) {
    const cleanedData = { ...body };

    Object.keys(cleanedData).forEach((key) => {
      if (cleanedData[key] && typeof cleanedData[key] === 'string') {
        const value = cleanedData[key] as string;
        if (value.includes('<') || value.includes('&')) {
          cleanedData[key] = cleanNotificationBody(value);
        }
      }
    });

    return cleanedData as T;
  }

  return body;
}
