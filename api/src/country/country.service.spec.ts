import { Test, TestingModule } from '@nestjs/testing';
import { CountryService } from './country.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { CountryDto } from './country.dto';
import { CacheService } from '@app/shared/redis/cache.service';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { User, user_roles } from '@/db/schema';
import { Country } from '@/db/schema/countries';
import { queryParamsDto } from '@/common/dto/query-params.dto';

describe('CountryService', () => {
  let service: CountryService;
  let drizzleService: DrizzleService;

  beforeEach(async () => {
    // Create a more realistic mock of the DrizzleService
    const mockDrizzleService = {
      db: {
        transaction: jest.fn((callback) => callback(mockDrizzleService.db)),
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        $count: jest.fn().mockResolvedValue(2),
        execute: jest.fn(),
        query: {
          countries: {
            findFirst: jest.fn(),
          },
          institutions: {
            findFirst: jest.fn(),
          },
        },
      },
    } as any;

    const mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      invalidateMany: jest.fn(),
      generateKey: jest
        .fn()
        .mockImplementation(
          (keys, prefix) =>
            `${prefix}:${Array.isArray(keys) ? keys.join(':') : keys}`,
        ),
      generateResourceKey: jest
        .fn()
        .mockImplementation((id, prefix) => `${prefix}:${id}`),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CountryService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<CountryService>(CountryService);
    drizzleService = module.get<DrizzleService>(DrizzleService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('addCountry', () => {
    it('should successfully add a new country', async () => {
      const countryDto: CountryDto = {
        name: 'Test Country',
        code: 'TC',
      };

      const mockNewCountry = { id: '1', ...countryDto };

      (drizzleService.db.select as jest.Mock).mockImplementation(
        () =>
          ({
            from: () => ({
              where: () => ({
                limit: () => [],
              }),
            }),
          }) as any,
      );

      (drizzleService.db.insert as jest.Mock).mockImplementation(
        () =>
          ({
            values: () => ({
              returning: () => [mockNewCountry],
            }),
          }) as any,
      );

      const result = await service.addCountry(countryDto);
      expect(result).toEqual(mockNewCountry);
    });

    it('should throw ConflictException if country already exists', async () => {
      const countryDto: CountryDto = {
        name: 'Test Country',
        code: 'TC',
      };

      (drizzleService.db.select as jest.Mock).mockImplementation(
        () =>
          ({
            from: () => ({
              where: () => ({
                limit: () => [{ id: '1', ...countryDto }],
              }),
            }),
          }) as any,
      );

      await expect(service.addCountry(countryDto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('getAllCountries', () => {
    it('should return all countries with pagination', async () => {
      const user: User = {
        id: '1',
        role: user_roles.ADMIN,
      } as User;

      const queryParams: queryParamsDto & {
        all?: boolean;
        sort: keyof Country;
      } = {
        sort: 'name',
        order: 'asc',
        limit: 10,
        page: 1,
        search: '',
        all: false,
      };

      const mockCountries = [
        { id: '1', name: 'Country A', code: 'CA' },
        { id: '2', name: 'Country B', code: 'CB' },
      ];

      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              where: () => ({
                orderBy: () => ({
                  limit: () => ({
                    offset: () => mockCountries,
                  }),
                }),
              }),
            }),
          }) as any,
      );

      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              where: () => [{ count: '2' }],
            }),
          }) as any,
      );

      const result = await service.getAllCountries(user, queryParams);
      expect(result).toEqual({
        data: mockCountries,
        total: 2,
      });
    });

    it('should return cached data when available and all=true', async () => {
      const user: User = {
        id: '1',
        role: user_roles.ADMIN,
      } as User;

      const queryParams = {
        sort: 'name' as keyof Country,
        order: 'asc',
        limit: 10,
        page: 1,
        all: true,
      };

      const mockCachedData = {
        data: [{ id: '1', name: 'Cached Country', code: 'CC' }],
        total: 1,
      };

      const mockCacheService = service['cacheService'];
      (mockCacheService.get as jest.Mock).mockResolvedValue(mockCachedData);

      const result = await service.getAllCountries(user, queryParams);

      expect(result).toEqual(mockCachedData);
      expect(mockCacheService.get).toHaveBeenCalled();
    });

    it('should fetch from database and cache when cache miss and all=true', async () => {
      const user: User = {
        id: '1',
        role: user_roles.ADMIN,
      } as User;

      const queryParams = {
        sort: 'name' as keyof Country,
        order: 'asc',
        limit: 10,
        page: 1,
        all: true,
      };

      const mockCountries = [
        { id: '1', name: 'Country A', code: 'CA' },
        { id: '2', name: 'Country B', code: 'CB' },
      ];

      const mockCacheService = service['cacheService'];
      (mockCacheService.get as jest.Mock).mockResolvedValue(null);
      (mockCacheService.set as jest.Mock).mockResolvedValue(undefined);

      // Mock the private method getCountriesFromDB
      jest.spyOn(service as any, 'getCountriesFromDB').mockResolvedValue({
        data: mockCountries,
        total: 2,
      });

      const result = await service.getAllCountries(user, queryParams);

      expect(mockCacheService.get).toHaveBeenCalled();
      expect(mockCacheService.set).toHaveBeenCalled();
      expect(result).toEqual({
        data: mockCountries,
        total: 2,
      });
    });

    it('should skip cache when search is provided', async () => {
      const user: User = {
        id: '1',
        role: user_roles.ADMIN,
      } as User;

      const queryParams = {
        sort: 'name' as keyof Country,
        order: 'asc',
        limit: 10,
        page: 1,
        all: true,
        search: 'test',
      };

      const mockCountries = [{ id: '1', name: 'Test Country', code: 'TC' }];

      const mockCacheService = service['cacheService'];
      const getSpy = jest.spyOn(mockCacheService, 'get');

      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              where: () => ({
                orderBy: () => ({
                  limit: () => ({
                    offset: () => mockCountries,
                  }),
                }),
              }),
            }),
          }) as any,
      );

      (drizzleService.db.$count as jest.Mock).mockResolvedValue(1);

      await service.getAllCountries(user, queryParams);

      expect(getSpy).not.toHaveBeenCalled();
    });

    it('should handle cache failure gracefully', async () => {
      const user: User = {
        id: '1',
        role: user_roles.ADMIN,
      } as User;

      const queryParams = {
        sort: 'name' as keyof Country,
        order: 'asc',
        limit: 10,
        page: 1,
        all: true,
      };

      const mockCountries = [{ id: '1', name: 'Country A', code: 'CA' }];

      const mockCacheService = service['cacheService'];
      // Mock cache get to return null (cache miss)
      (mockCacheService.get as jest.Mock).mockResolvedValue(null);

      // Mock the private method getCountriesFromDB
      jest.spyOn(service as any, 'getCountriesFromDB').mockResolvedValue({
        data: mockCountries,
        total: 1,
      });

      const result = await service.getAllCountries(user, queryParams);

      expect(result).toEqual({
        data: mockCountries,
        total: 1,
      });
    });
  });

  describe('findCountryById', () => {
    it('should return a country when found', async () => {
      const mockCountry = {
        id: '1',
        name: 'Test Country',
        code: 'TC',
      };

      (
        drizzleService.db.query.countries.findFirst as jest.Mock
      ).mockResolvedValue(mockCountry);

      const result = await service.findCountryById('1');
      expect(result).toEqual(mockCountry);
    });

    it('should throw NotFoundException when country not found', async () => {
      (
        drizzleService.db.query.countries.findFirst as jest.Mock
      ).mockResolvedValue(null);

      await expect(service.findCountryById('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('getAllActiveCountries', () => {
    it('should return all active countries', async () => {
      const mockActiveCountries = [
        { id: '1', name: 'Active Country 1', code: 'AC1', disabled: false },
        { id: '2', name: 'Active Country 2', code: 'AC2', disabled: false },
      ];

      (drizzleService.db.select as jest.Mock).mockImplementation(
        () =>
          ({
            from: () => ({
              where: () => mockActiveCountries,
            }),
          }) as any,
      );

      const result = await service.getAllActiveCountries();

      expect(result).toEqual(mockActiveCountries);
    });
  });

  describe('getDisabledCountries', () => {
    it('should return all disabled countries', async () => {
      const mockDisabledCountries = [
        { id: '3', name: 'Disabled Country', code: 'DC', disabled: true },
      ];

      (drizzleService.db.select as jest.Mock).mockImplementation(
        () =>
          ({
            from: () => ({
              where: () => mockDisabledCountries,
            }),
          }) as any,
      );

      const result = await service.getDisabledCountries();

      expect(result).toEqual(mockDisabledCountries);
    });
  });

  describe('updateCountry', () => {
    it('should successfully update a country', async () => {
      const updateDto = {
        name: 'Updated Country',
        code: 'UC',
      };

      const mockUpdatedCountry = { id: '1', ...updateDto };

      (drizzleService.db.update as jest.Mock).mockImplementation(
        () =>
          ({
            set: () => ({
              where: () => ({
                returning: () => [mockUpdatedCountry],
              }),
            }),
          }) as any,
      );

      const result = await service.updateCountry('1', updateDto);

      expect(result).toEqual([mockUpdatedCountry]);
    });

    it('should throw NotFoundException when country to update not found', async () => {
      const updateDto = {
        name: 'Updated Country',
      };

      (drizzleService.db.update as jest.Mock).mockImplementation(
        () =>
          ({
            set: () => ({
              where: () => ({
                returning: () => [],
              }),
            }),
          }) as any,
      );

      await expect(service.updateCountry('999', updateDto)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should handle partial updates', async () => {
      const updateDto = {
        name: 'Updated Name Only',
      };

      const mockUpdatedCountry = {
        id: '1',
        name: 'Updated Name Only',
        code: 'TC',
      };

      (drizzleService.db.update as jest.Mock).mockImplementation(
        () =>
          ({
            set: () => ({
              where: () => ({
                returning: () => [mockUpdatedCountry],
              }),
            }),
          }) as any,
      );

      const result = await service.updateCountry('1', updateDto);

      expect(result).toEqual([mockUpdatedCountry]);
    });

    it('should throw BadRequestException when id is not provided', async () => {
      const updateDto = {
        name: 'Updated Country',
      };

      await expect(service.updateCountry('', updateDto)).rejects.toThrow(
        'ID is required',
      );
    });
  });

  describe('deleteCountryById', () => {
    it('should successfully delete a country', async () => {
      const mockDeletedCountry = { id: '1', name: 'Test Country', code: 'TC' };

      // Mock findCountryById to return existing country
      (
        drizzleService.db.query.countries.findFirst as jest.Mock
      ).mockResolvedValue(mockDeletedCountry);

      // Mock no institutions check
      (
        drizzleService.db.query.institutions.findFirst as jest.Mock
      ).mockResolvedValue(undefined);

      // Mock successful delete
      (drizzleService.db.delete as jest.Mock).mockImplementation(
        () =>
          ({
            where: () => ({
              returning: () => [mockDeletedCountry],
            }),
          }) as any,
      );

      const result = await service.deleteCountryById('1');

      expect(result).toEqual([mockDeletedCountry]);
    });

    it('should throw BadRequestException when country has institutions', async () => {
      const mockCountry = { id: '1', name: 'Test Country', code: 'TC' };

      // Mock findCountryById to return existing country
      (
        drizzleService.db.query.countries.findFirst as jest.Mock
      ).mockResolvedValue(mockCountry);

      // Mock institutions found
      (
        drizzleService.db.query.institutions.findFirst as jest.Mock
      ).mockResolvedValue({
        id: '1',
        name: 'Test Institution',
      });

      await expect(service.deleteCountryById('1')).rejects.toThrow(
        'Cannot delete country that has associated institutions',
      );
    });

    it('should throw NotFoundException when country to delete not found', async () => {
      // Mock findCountryById to throw NotFoundException
      (
        drizzleService.db.query.countries.findFirst as jest.Mock
      ).mockResolvedValue(undefined);

      await expect(service.deleteCountryById('999')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw BadRequestException when id is not provided', async () => {
      await expect(service.deleteCountryById('')).rejects.toThrow(
        'ID is required',
      );
    });
  });

  describe('disableCountry', () => {
    it('should successfully disable a country', async () => {
      const mockDisabledCountry = {
        id: '1',
        name: 'Test Country',
        code: 'TC',
        disabled: true,
      };

      (drizzleService.db.update as jest.Mock).mockImplementation(
        () =>
          ({
            set: () => ({
              where: () => ({
                returning: () => [mockDisabledCountry],
              }),
            }),
          }) as any,
      );

      const result = await service.disableCountry('1');

      expect(result).toEqual([mockDisabledCountry]);
    });

    it('should throw BadRequestException when id is not provided', async () => {
      await expect(service.disableCountry('')).rejects.toThrow(
        'ID is required',
      );
    });
  });

  describe('enableCountry', () => {
    it('should successfully enable a country', async () => {
      const mockEnabledCountry = {
        id: '1',
        name: 'Test Country',
        code: 'TC',
        disabled: false,
      };

      (drizzleService.db.update as jest.Mock).mockImplementation(
        () =>
          ({
            set: () => ({
              where: () => ({
                returning: () => [mockEnabledCountry],
              }),
            }),
          }) as any,
      );

      const result = await service.enableCountry('1');

      expect(result).toEqual([mockEnabledCountry]);
    });

    it('should throw BadRequestException when id is not provided', async () => {
      await expect(service.enableCountry('')).rejects.toThrow('ID is required');
    });
  });
});
