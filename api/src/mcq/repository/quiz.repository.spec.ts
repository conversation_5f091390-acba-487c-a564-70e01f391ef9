import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { QuizRepository } from './quiz.repository';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { QuestionRepository } from './questions.repository';
import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
import { quizSchema, quizScoreSchema } from '@/db/schema/quiz';
import { student_profiles } from '@/db/schema';

describe('QuizRepository', () => {
  let repository: QuizRepository;
  let mockDrizzleService: jest.Mocked<DrizzleService>;
  let mockQuestionRepository: jest.Mocked<QuestionRepository>;
  let mockPointSystemRepository: jest.Mocked<PointSystemRepository>;

  const mockQuiz = {
    id: '123e4567-e89b-12d3-a456-************',
    title: 'JavaScript Fundamentals Quiz',
    question_bank_id: '456e7890-e89b-12d3-a456-************',
    created_by: '789e0123-e89b-12d3-a456-************',
    start_time: '2024-01-01T10:00:00Z',
    end_at: '2024-01-01T12:00:00Z',
    total_questions: 10,
    time_per_question: 60,
    status: 'active',
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockQuizScore = {
    id: '012e3456-e89b-12d3-a456-************',
    quiz_id: '123e4567-e89b-12d3-a456-************',
    user_id: '345e6789-e89b-12d3-a456-************',
    score: 85,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    // Create mock services
    mockDrizzleService = {
      db: {
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        returning: jest.fn(),
        execute: jest.fn(),
        $count: jest.fn(),
      },
    } as any;

    mockQuestionRepository = {
      getQuiz: jest.fn(),
    } as any;

    mockPointSystemRepository = {
      awardPointsToStudent: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuizRepository,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: QuestionRepository,
          useValue: mockQuestionRepository,
        },
        {
          provide: PointSystemRepository,
          useValue: mockPointSystemRepository,
        },
      ],
    }).compile();

    repository = module.get<QuizRepository>(QuizRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createQuiz', () => {
    it('should be defined', () => {
      expect(repository).toBeDefined();
    });

    it('should create a quiz successfully', async () => {
      const quizData = {
        title: 'New Quiz',
        question_bank_id: '456e7890-e89b-12d3-a456-************',
        created_by: '789e0123-e89b-12d3-a456-************',
        start_time: '2024-02-01T10:00:00Z',
        end_at: '2024-02-01T12:00:00Z',
        total_questions: 15,
        time_per_question: 90,
        status: 'inactive',
      };

      mockDrizzleService.db.returning.mockResolvedValue([mockQuiz]);

      const result = await repository.createQuiz(quizData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalledWith(quizSchema);
      expect(mockDrizzleService.db.values).toHaveBeenCalledWith(quizData);
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual([mockQuiz]);
    });

    it('should create quiz with custom transaction', async () => {
      const quizData = { title: 'Test Quiz' };
      const mockTx = {
        insert: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockQuiz]),
      };

      const result = await repository.createQuiz(quizData, mockTx as any);

      expect(mockTx.insert).toHaveBeenCalledWith(quizSchema);
      expect(mockTx.values).toHaveBeenCalledWith(quizData);
      expect(result).toEqual([mockQuiz]);
    });

    it('should propagate database errors', async () => {
      const quizData = { title: 'Test Quiz' };
      const dbError = new Error('Database constraint violation');

      mockDrizzleService.db.returning.mockRejectedValue(dbError);

      await expect(repository.createQuiz(quizData)).rejects.toThrow(dbError);
    });
  });

  describe('getQuizById', () => {
    const quizId = '123e4567-e89b-12d3-a456-************';

    beforeEach(() => {
      // Reset the mock to return the select chain
      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn(),
        }),
      });
    });

    it('should get quiz by id successfully', async () => {
      mockDrizzleService.db.select().from().where.mockResolvedValue([mockQuiz]);

      const result = await repository.getQuizById(quizId);

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockDrizzleService.db.select().from).toHaveBeenCalledWith(
        quizSchema,
      );
      expect(result).toEqual(mockQuiz);
    });

    it('should throw NotFoundException when quiz not found', async () => {
      mockDrizzleService.db.select().from().where.mockResolvedValue([]);

      await expect(repository.getQuizById(quizId)).rejects.toThrow(
        new NotFoundException(`Quiz with ID ${quizId} not found`),
      );
    });

    it('should throw NotFoundException when quiz result is null', async () => {
      mockDrizzleService.db.select().from().where.mockResolvedValue([null]);

      await expect(repository.getQuizById(quizId)).rejects.toThrow(
        new NotFoundException(`Quiz with ID ${quizId} not found`),
      );
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database connection failed');
      mockDrizzleService.db.select().from().where.mockRejectedValue(dbError);

      await expect(repository.getQuizById(quizId)).rejects.toThrow(dbError);
    });
  });

  describe('createStudentScore', () => {
    it('should create student score successfully', async () => {
      const scoreData = {
        quiz_id: '123e4567-e89b-12d3-a456-************',
        user_id: '345e6789-e89b-12d3-a456-************',
        score: 75,
      };

      mockDrizzleService.db.returning.mockResolvedValue([mockQuizScore]);

      const result = await repository.createStudentScore(scoreData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalledWith(
        quizScoreSchema,
      );
      expect(mockDrizzleService.db.values).toHaveBeenCalledWith(scoreData);
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual([mockQuizScore]);
    });

    it('should create student score with custom transaction', async () => {
      const scoreData = { quiz_id: 'quiz-1', user_id: 'user-1', score: 90 };
      const mockTx = {
        insert: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockQuizScore]),
      };

      const result = await repository.createStudentScore(
        scoreData,
        mockTx as any,
      );

      expect(mockTx.insert).toHaveBeenCalledWith(quizScoreSchema);
      expect(result).toEqual([mockQuizScore]);
    });

    it('should handle score creation errors', async () => {
      const scoreData = { quiz_id: 'quiz-1', user_id: 'user-1', score: 80 };
      const dbError = new Error('Foreign key constraint violation');

      mockDrizzleService.db.returning.mockRejectedValue(dbError);

      await expect(repository.createStudentScore(scoreData)).rejects.toThrow(
        dbError,
      );
    });
  });

  describe('updateQuiz', () => {
    const quizId = '123e4567-e89b-12d3-a456-************';

    it('should update quiz successfully', async () => {
      const updateData = {
        title: 'Updated Quiz Title',
        question_bank_id: '456e7890-e89b-12d3-a456-************',
        created_by: '789e0123-e89b-12d3-a456-************',
        start_time: '2024-02-01T10:00:00Z',
        end_at: '2024-02-01T12:00:00Z',
        total_questions: 20,
        time_per_question: 120,
        status: 'active',
      };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockQuiz, ...updateData },
      ]);

      const result = await repository.updateQuiz(updateData, quizId);

      expect(mockDrizzleService.db.update).toHaveBeenCalledWith(quizSchema);
      expect(mockDrizzleService.db.set).toHaveBeenCalled();
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual({
        message: 'Quiz updated successfully',
        statusCode: 200,
      });
    });

    it('should throw NotFoundException when quiz not found for update', async () => {
      const updateData = { title: 'Updated Title' };
      mockDrizzleService.db.returning.mockResolvedValue([]);

      await expect(repository.updateQuiz(updateData, quizId)).rejects.toThrow(
        new NotFoundException(`Quiz with ID ${quizId} not found`),
      );
    });

    it('should handle update with custom transaction', async () => {
      const updateData = { title: 'Updated Title' };
      const mockTx = {
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockQuiz]),
      };

      const result = await repository.updateQuiz(
        updateData,
        quizId,
        mockTx as any,
      );

      expect(mockTx.update).toHaveBeenCalledWith(quizSchema);
      expect(result).toEqual({
        message: 'Quiz updated successfully',
        statusCode: 200,
      });
    });
  });

  describe('deleteQuiz', () => {
    const quizId = '123e4567-e89b-12d3-a456-************';

    it('should delete quiz successfully', async () => {
      mockDrizzleService.db.returning.mockResolvedValue([mockQuiz]);

      const result = await repository.deleteQuiz(quizId);

      expect(mockDrizzleService.db.delete).toHaveBeenCalledWith(quizSchema);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual({
        message: 'Quiz deleted successfully',
        statusCode: 200,
      });
    });

    it('should throw NotFoundException when quiz not found for deletion', async () => {
      mockDrizzleService.db.returning.mockResolvedValue([]);

      await expect(repository.deleteQuiz(quizId)).rejects.toThrow(
        new NotFoundException(`Quiz with ID ${quizId} not found`),
      );
    });

    it('should delete quiz with custom transaction', async () => {
      const mockTx = {
        delete: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockQuiz]),
      };

      const result = await repository.deleteQuiz(quizId, mockTx as any);

      expect(mockTx.delete).toHaveBeenCalledWith(quizSchema);
      expect(result).toEqual({
        message: 'Quiz deleted successfully',
        statusCode: 200,
      });
    });
  });

  describe('getStudentScore', () => {
    const quizId = '123e4567-e89b-12d3-a456-************';
    const userId = '345e6789-e89b-12d3-a456-************';

    beforeEach(() => {
      // Reset the mock to return the select chain
      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn(),
        }),
      });
    });

    it('should get student score successfully', async () => {
      mockDrizzleService.db
        .select()
        .from()
        .where.mockResolvedValue([mockQuizScore]);

      const result = await repository.getStudentScore(quizId, userId);

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockDrizzleService.db.select().from).toHaveBeenCalledWith(
        quizScoreSchema,
      );
      expect(result).toEqual([mockQuizScore]);
    });

    it('should throw NotFoundException when student score not found', async () => {
      mockDrizzleService.db.select().from().where.mockResolvedValue([]);

      await expect(repository.getStudentScore(quizId, userId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('updateQuizScore', () => {
    it('should update quiz score successfully', async () => {
      const scoreData = {
        quiz_id: '123e4567-e89b-12d3-a456-************',
        user_id: '345e6789-e89b-12d3-a456-************',
        score: 95,
      };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockQuizScore, score: 95 },
      ]);

      const result = await repository.updateQuizScore(scoreData);

      expect(mockDrizzleService.db.update).toHaveBeenCalledWith(
        quizScoreSchema,
      );
      expect(mockDrizzleService.db.set).toHaveBeenCalledWith({ score: 95 });
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual([{ ...mockQuizScore, score: 95 }]);
    });

    it('should handle update score errors', async () => {
      const scoreData = { quiz_id: 'quiz-1', user_id: 'user-1', score: 85 };
      const dbError = new Error('Update failed');

      mockDrizzleService.db.returning.mockRejectedValue(dbError);

      await expect(repository.updateQuizScore(scoreData)).rejects.toThrow(
        dbError,
      );
    });
  });

  describe('getStudentId', () => {
    const userId = '345e6789-e89b-12d3-a456-************';

    beforeEach(() => {
      // Reset the mock to return the select chain
      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn(),
        }),
      });
    });

    it('should get student id successfully', async () => {
      const studentId = 'student-123';
      mockDrizzleService.db
        .select()
        .from()
        .where.mockResolvedValue([{ id: studentId }]);

      const result = await repository.getStudentId(userId);

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockDrizzleService.db.select().from).toHaveBeenCalledWith(
        student_profiles,
      );
      expect(result).toEqual(studentId);
    });

    it('should throw NotFoundException when student not found', async () => {
      mockDrizzleService.db.select().from().where.mockResolvedValue([]);

      await expect(repository.getStudentId(userId)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw NotFoundException when student result is null', async () => {
      mockDrizzleService.db.select().from().where.mockResolvedValue([null]);

      await expect(repository.getStudentId(userId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('checkAndActivateQuiz', () => {
    beforeEach(() => {
      // Reset the mock to return the select chain
      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn(),
        }),
      });
    });

    it('should activate quizzes that are due', async () => {
      const inactiveQuizzes = [
        { ...mockQuiz, status: 'inactive' },
        { ...mockQuiz, id: 'quiz-2', status: 'inactive' },
      ];

      mockDrizzleService.db
        .select()
        .from()
        .where.mockResolvedValue(inactiveQuizzes);
      mockDrizzleService.db.returning.mockResolvedValue([]);

      const result = await repository.checkAndActivateQuiz();

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockDrizzleService.db.select().from).toHaveBeenCalledWith(
        quizSchema,
      );
      expect(result.message).toContain('Quiz status updated');
      expect(result.result).toEqual(inactiveQuizzes);
    });

    it('should return message when no quizzes are due', async () => {
      mockDrizzleService.db.select().from().where.mockResolvedValue([]);

      const result = await repository.checkAndActivateQuiz();

      expect(result.message).toContain('No quiz is due for activation');
      expect(result.result).toEqual([]);
    });

    it('should handle activation errors', async () => {
      const dbError = new Error('Activation failed');
      mockDrizzleService.db.select().from().where.mockRejectedValue(dbError);

      await expect(repository.checkAndActivateQuiz()).rejects.toThrow(dbError);
    });
  });

  describe('getAvailableQuizzes', () => {
    beforeEach(() => {
      // Reset the mock to return the select chain
      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            orderBy: jest.fn(),
          }),
        }),
      });
    });

    it('should get available quizzes successfully', async () => {
      const availableQuizzes = [mockQuiz, { ...mockQuiz, id: 'quiz-2' }];
      mockDrizzleService.db
        .select()
        .from()
        .where()
        .orderBy.mockResolvedValue(availableQuizzes);

      const result = await repository.getAvailableQuizzes();

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockDrizzleService.db.select().from).toHaveBeenCalledWith(
        quizSchema,
      );
      expect(mockDrizzleService.db.select().from().where).toHaveBeenCalled();
      expect(
        mockDrizzleService.db.select().from().where().orderBy,
      ).toHaveBeenCalled();
      expect(result).toEqual(availableQuizzes);
    });

    it('should throw NotFoundException when no available quizzes found', async () => {
      mockDrizzleService.db
        .select()
        .from()
        .where()
        .orderBy.mockResolvedValue([]);

      await expect(repository.getAvailableQuizzes()).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
