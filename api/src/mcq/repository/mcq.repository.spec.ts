import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { McqRepository } from './mcq.repository';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { questionBank } from '@/db/schema/mcq';

describe('McqRepository', () => {
  let repository: McqRepository;
  let mockDrizzleService: jest.Mocked<DrizzleService>;

  const mockQuestionBank = {
    id: '123e4567-e89b-12d3-a456-************',
    name: 'JavaScript Fundamentals',
    stack: 'Frontend',
    framework: 'React',
    imageUrl: 'https://example.com/js-logo.png',
    createdBy: '456e7890-e89b-12d3-a456-************',
    created_at: new Date(),
    updated_at: '2024-01-01T00:00:00Z',
  };

  beforeEach(async () => {
    // Create mock DrizzleService
    mockDrizzleService = {
      db: {
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        returning: jest.fn(),
        execute: jest.fn(),
      },
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        McqRepository,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
      ],
    }).compile();

    repository = module.get<McqRepository>(McqRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createQuestionBank', () => {
    it('should be defined', () => {
      expect(repository).toBeDefined();
    });

    it('should create a question bank successfully', async () => {
      const questionBankData = {
        name: 'Python Basics',
        stack: 'Backend',
        framework: 'Django',
        imageUrl: 'https://example.com/python-logo.png',
        createdBy: '456e7890-e89b-12d3-a456-************',
      };

      mockDrizzleService.db.returning.mockResolvedValue([mockQuestionBank]);

      const result = await repository.createQuestionBank(questionBankData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalledWith(questionBank);
      expect(mockDrizzleService.db.values).toHaveBeenCalledWith(
        questionBankData,
      );
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual([mockQuestionBank]);
    });

    it('should create question bank with custom transaction', async () => {
      const questionBankData = {
        name: 'Node.js Advanced',
        stack: 'Backend',
        framework: 'Express',
        imageUrl: 'https://example.com/node-logo.png',
        createdBy: '456e7890-e89b-12d3-a456-************',
      };

      const mockTx = {
        insert: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockQuestionBank]),
      };

      const result = await repository.createQuestionBank(
        questionBankData,
        mockTx as any,
      );

      expect(mockTx.insert).toHaveBeenCalledWith(questionBank);
      expect(mockTx.values).toHaveBeenCalledWith(questionBankData);
      expect(mockTx.returning).toHaveBeenCalled();
      expect(result).toEqual([mockQuestionBank]);
    });

    it('should handle question bank creation with all required fields', async () => {
      const completeQuestionBankData = {
        name: 'TypeScript Mastery',
        stack: 'Frontend',
        framework: 'Angular',
        imageUrl: 'https://example.com/ts-logo.png',
        createdBy: '456e7890-e89b-12d3-a456-************',
      };

      mockDrizzleService.db.returning.mockResolvedValue([mockQuestionBank]);

      const result = await repository.createQuestionBank(
        completeQuestionBankData,
      );

      expect(mockDrizzleService.db.values).toHaveBeenCalledWith(
        completeQuestionBankData,
      );
      expect(result).toEqual([mockQuestionBank]);
    });
  });

  describe('updateQuestionBank', () => {
    const questionBankId = '123e4567-e89b-12d3-a456-************';

    it('should update a question bank successfully', async () => {
      const updateData = {
        name: 'Updated JavaScript Fundamentals',
        imageUrl: 'https://example.com/updated-js-logo.png',
      };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockQuestionBank, ...updateData },
      ]);

      const result = await repository.updateQuestionBank(
        updateData,
        questionBankId,
      );

      expect(mockDrizzleService.db.update).toHaveBeenCalledWith(questionBank);
      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(updateData);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual([{ ...mockQuestionBank, ...updateData }]);
    });

    it('should update question bank with custom transaction', async () => {
      const updateData = { name: 'Updated Name' };
      const mockTx = {
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest
          .fn()
          .mockResolvedValue([{ ...mockQuestionBank, ...updateData }]),
      };

      const result = await repository.updateQuestionBank(
        updateData,
        questionBankId,
        mockTx as any,
      );

      expect(mockTx.update).toHaveBeenCalledWith(questionBank);
      expect(mockTx.set).toHaveBeenCalledWith(updateData);
      expect(result).toEqual([{ ...mockQuestionBank, ...updateData }]);
    });

    it('should handle partial updates', async () => {
      const updateData = { stack: 'Full Stack' };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockQuestionBank, ...updateData },
      ]);

      const result = await repository.updateQuestionBank(
        updateData,
        questionBankId,
      );

      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(updateData);
      expect(result).toEqual([{ ...mockQuestionBank, ...updateData }]);
    });

    it('should handle framework updates', async () => {
      const updateData = { framework: 'Vue.js' };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockQuestionBank, ...updateData },
      ]);

      const result = await repository.updateQuestionBank(
        updateData,
        questionBankId,
      );

      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(updateData);
      expect(result).toEqual([{ ...mockQuestionBank, ...updateData }]);
    });
  });

  describe('deleteQuestionBank', () => {
    const questionBankId = '123e4567-e89b-12d3-a456-************';

    it('should delete a question bank successfully', async () => {
      mockDrizzleService.db.returning.mockResolvedValue([mockQuestionBank]);

      const result = await repository.deleteQuestionBank(questionBankId);

      expect(mockDrizzleService.db.delete).toHaveBeenCalledWith(questionBank);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual({
        message: 'Question bank deleted successfully',
        statusCode: 200,
      });
    });

    it('should delete question bank with custom transaction', async () => {
      const mockTx = {
        delete: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockQuestionBank]),
      };

      const result = await repository.deleteQuestionBank(
        questionBankId,
        mockTx as any,
      );

      expect(mockTx.delete).toHaveBeenCalledWith(questionBank);
      expect(result).toEqual({
        message: 'Question bank deleted successfully',
        statusCode: 200,
      });
    });

    it('should throw NotFoundException when question bank not found', async () => {
      mockDrizzleService.db.returning.mockResolvedValue([]);

      await expect(
        repository.deleteQuestionBank(questionBankId),
      ).rejects.toThrow(
        new NotFoundException(
          `Question bank with ID ${questionBankId} not found`,
        ),
      );

      expect(mockDrizzleService.db.delete).toHaveBeenCalledWith(questionBank);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
    });

    it('should propagate database errors', async () => {
      const dbError = new Error('Database connection failed');
      mockDrizzleService.db.returning.mockRejectedValue(dbError);

      await expect(
        repository.deleteQuestionBank(questionBankId),
      ).rejects.toThrow(dbError);
    });
  });

  describe('getQuestionBankById', () => {
    const questionBankId = '123e4567-e89b-12d3-a456-************';

    it('should get question bank by id successfully', async () => {
      mockDrizzleService.db.execute.mockResolvedValue([mockQuestionBank]);

      const result = await repository.getQuestionBankById(questionBankId);

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockDrizzleService.db.from).toHaveBeenCalledWith(questionBank);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.execute).toHaveBeenCalled();
      expect(result).toEqual([mockQuestionBank]);
    });

    it('should get question bank with custom transaction', async () => {
      const mockTx = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([mockQuestionBank]),
      };

      const result = await repository.getQuestionBankById(
        questionBankId,
        mockTx as any,
      );

      expect(mockTx.select).toHaveBeenCalled();
      expect(mockTx.from).toHaveBeenCalledWith(questionBank);
      expect(result).toEqual([mockQuestionBank]);
    });

    it('should return empty array when question bank not found', async () => {
      mockDrizzleService.db.execute.mockResolvedValue([]);

      const result = await repository.getQuestionBankById(questionBankId);

      expect(result).toEqual([]);
    });

    it('should handle database errors gracefully', async () => {
      const dbError = new Error('Database query failed');
      mockDrizzleService.db.execute.mockRejectedValue(dbError);

      await expect(
        repository.getQuestionBankById(questionBankId),
      ).rejects.toThrow(dbError);
    });
  });
});
