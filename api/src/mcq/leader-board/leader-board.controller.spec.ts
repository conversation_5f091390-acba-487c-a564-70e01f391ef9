import { Test, TestingModule } from '@nestjs/testing';
import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AccessControlModule } from 'nest-access-control';
import { LeaderBoardController } from './leader-board.controller';
import { LeaderBoardService } from './leader-board.service';
import { PeriodEnumType } from './leader-board.types';
import type { User as IUser } from '@/db/schema';
import { RBAC_ROLES } from '@/auth/app.roles';

describe('LeaderBoardController', () => {
  let controller: LeaderBoardController;
  let mockLeaderBoardService: jest.Mocked<LeaderBoardService>;

  const mockUser: IUser = {
    id: 'user-123',
    email: '<EMAIL>',
    role: 'student',
    state: 'active',
    profile_pic_url: 'https://example.com/pic.jpg',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    deleted: false,
    student_profile: {
      id: 'student-123',
      user_id: 'user-123',
      first_name: 'John',
      last_name: 'Doe',
      institution_id: 'inst-123',
      degree: 'Computer Science',
      programme: 'BSc',
      level: '3',
      graduation_date: '2024-06-01T00:00:00Z',
      enrollment_date: '2021-09-01T00:00:00Z',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  } as IUser;

  const mockUserWithoutProfile: IUser = {
    id: 'user-456',
    email: '<EMAIL>',
    role: 'student',
    state: 'active',
    profile_pic_url: null,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    deleted: false,
    student_profile: null,
  } as IUser;

  beforeEach(async () => {
    mockLeaderBoardService = {
      getStudentRank: jest.fn(),
      getLeaderBoardByFilterPeriod: jest.fn(),
      getLeaderBoardByRank: jest.fn(),
      refreshMaterializedViews: jest.fn(),
      forceRefreshMaterializedViews: jest.fn(),
      getLeaderBoardFilterPeriodWeb: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      imports: [AccessControlModule.forRoles(RBAC_ROLES, {})],
      controllers: [LeaderBoardController],
      providers: [
        {
          provide: LeaderBoardService,
          useValue: mockLeaderBoardService,
        },
        Reflector,
      ],
    }).compile();

    controller = module.get<LeaderBoardController>(LeaderBoardController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getStudentRank', () => {
    it('should be defined', () => {
      expect(controller).toBeDefined();
    });

    it('should get student rank successfully', async () => {
      const query = { period: PeriodEnumType.WEEKLY };
      const mockRankData = {
        currentUser: {
          student_id: 'student-123',
          first_name: 'John',
          last_name: 'Doe',
          total_score: 150,
          rank: 5,
        },
        rank: 5,
        timestamp: Date.now(),
      };

      mockLeaderBoardService.getStudentRank.mockResolvedValue(mockRankData);

      const result = await controller.getStudentRank(query, mockUser);

      expect(mockLeaderBoardService.getStudentRank).toHaveBeenCalledWith(
        'student-123',
        PeriodEnumType.WEEKLY,
      );
      expect(result).toEqual(mockRankData);
    });

    it('should throw BadRequestException when user has no student profile', async () => {
      const query = { period: PeriodEnumType.WEEKLY };

      await expect(
        controller.getStudentRank(query, mockUserWithoutProfile),
      ).rejects.toThrow(
        new BadRequestException('User does not have a student profile'),
      );

      expect(mockLeaderBoardService.getStudentRank).not.toHaveBeenCalled();
    });

    it('should throw InternalServerErrorException when service fails', async () => {
      const query = { period: PeriodEnumType.DAILY };

      mockLeaderBoardService.getStudentRank.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(controller.getStudentRank(query, mockUser)).rejects.toThrow(
        new InternalServerErrorException('Failed to retrieve student rank'),
      );

      expect(mockLeaderBoardService.getStudentRank).toHaveBeenCalledWith(
        'student-123',
        PeriodEnumType.DAILY,
      );
    });

    it('should handle different period types', async () => {
      const periods = [
        PeriodEnumType.DAILY,
        PeriodEnumType.MONTHLY,
        PeriodEnumType.ALL_TIME,
      ];

      for (const period of periods) {
        const query = { period };
        const mockRankData = { rank: 1, timestamp: Date.now() };

        mockLeaderBoardService.getStudentRank.mockResolvedValue(mockRankData);

        await controller.getStudentRank(query, mockUser);

        expect(mockLeaderBoardService.getStudentRank).toHaveBeenCalledWith(
          'student-123',
          period,
        );
      }
    });
  });

  describe('getLeaderBoard', () => {
    it('should get leaderboard successfully', async () => {
      const query = {
        period: PeriodEnumType.WEEKLY,
        page: 1,
        limit: 10,
        search: 'John',
      };

      const mockLeaderboardData = {
        data: [
          {
            student_id: 'student-123',
            first_name: 'John',
            last_name: 'Doe',
            total_score: 150,
            rank: 1,
          },
        ],
        total: 1,
        currentUser: {
          student_id: 'student-123',
          rank: 1,
        },
        userPosition: 1,
      };

      mockLeaderBoardService.getLeaderBoardByFilterPeriod.mockResolvedValue(
        mockLeaderboardData,
      );

      const result = await controller.getLeaderBoard(query, mockUser);

      expect(
        mockLeaderBoardService.getLeaderBoardByFilterPeriod,
      ).toHaveBeenCalledWith(
        PeriodEnumType.WEEKLY,
        1,
        10,
        'student-123',
        'John',
      );
      expect(result).toEqual(mockLeaderboardData);
    });

    it('should get leaderboard without search parameter', async () => {
      const query = {
        period: PeriodEnumType.MONTHLY,
        page: 2,
        limit: 20,
      };

      const mockLeaderboardData = {
        data: [],
        total: 0,
        currentUser: null,
        userPosition: null,
      };

      mockLeaderBoardService.getLeaderBoardByFilterPeriod.mockResolvedValue(
        mockLeaderboardData,
      );

      const result = await controller.getLeaderBoard(query, mockUser);

      expect(
        mockLeaderBoardService.getLeaderBoardByFilterPeriod,
      ).toHaveBeenCalledWith(
        PeriodEnumType.MONTHLY,
        2,
        20,
        'student-123',
        undefined,
      );
      expect(result).toEqual(mockLeaderboardData);
    });

    it('should handle default pagination values', async () => {
      const query = {
        period: PeriodEnumType.ALL_TIME,
      };

      const mockLeaderboardData = {
        data: [],
        total: 0,
        currentUser: null,
        userPosition: null,
      };

      mockLeaderBoardService.getLeaderBoardByFilterPeriod.mockResolvedValue(
        mockLeaderboardData,
      );

      await controller.getLeaderBoard(query, mockUser);

      expect(
        mockLeaderBoardService.getLeaderBoardByFilterPeriod,
      ).toHaveBeenCalledWith(
        PeriodEnumType.ALL_TIME,
        undefined,
        undefined,
        'student-123',
        undefined,
      );
    });
  });

  describe('getLeaderBoardByRank', () => {
    it('should get leaderboard by rank successfully', async () => {
      const rank = 5;
      const period = PeriodEnumType.WEEKLY;
      const mockRankData = [
        {
          student_id: 'student-123',
          first_name: 'John',
          last_name: 'Doe',
          rank: 5,
          total_score: 100,
        },
      ];

      mockLeaderBoardService.getLeaderBoardByRank.mockResolvedValue(
        mockRankData,
      );

      const result = await controller.getLeaderBoardByRank(rank, period);

      expect(mockLeaderBoardService.getLeaderBoardByRank).toHaveBeenCalledWith(
        5,
        PeriodEnumType.WEEKLY,
      );
      expect(result).toEqual(mockRankData);
    });

    it('should handle different rank values', async () => {
      const ranks = [1, 10, 100];
      const period = PeriodEnumType.DAILY;

      for (const rank of ranks) {
        const mockRankData = [{ rank, total_score: 50 }];
        mockLeaderBoardService.getLeaderBoardByRank.mockResolvedValue(
          mockRankData,
        );

        await controller.getLeaderBoardByRank(rank, period);

        expect(
          mockLeaderBoardService.getLeaderBoardByRank,
        ).toHaveBeenCalledWith(rank, period);
      }
    });
  });

  describe('refreshLeaderBoardScore', () => {
    it('should refresh leaderboard successfully', async () => {
      mockLeaderBoardService.refreshMaterializedViews.mockResolvedValue(
        undefined,
      );

      const result = await controller.refreshLeaderBoardScore();

      expect(
        mockLeaderBoardService.refreshMaterializedViews,
      ).toHaveBeenCalled();
      expect(result).toEqual({
        message: 'Leaderboard refresh process completed successfully',
      });
    });

    it('should propagate service errors', async () => {
      const serviceError = new Error('Refresh failed');
      mockLeaderBoardService.refreshMaterializedViews.mockRejectedValue(
        serviceError,
      );

      await expect(controller.refreshLeaderBoardScore()).rejects.toThrow(
        serviceError,
      );

      expect(
        mockLeaderBoardService.refreshMaterializedViews,
      ).toHaveBeenCalled();
    });
  });

  describe('forceRefreshLeaderBoardScore', () => {
    it('should force refresh leaderboard successfully', async () => {
      mockLeaderBoardService.forceRefreshMaterializedViews.mockResolvedValue(
        undefined,
      );

      const result = await controller.forceRefreshLeaderBoardScore();

      expect(
        mockLeaderBoardService.forceRefreshMaterializedViews,
      ).toHaveBeenCalled();
      expect(result).toEqual({
        message: 'Leaderboard force refresh completed successfully',
        duration: expect.stringMatching(/^\d+ms$/),
      });
    });

    it('should propagate service errors for force refresh', async () => {
      const serviceError = new Error('Force refresh failed');
      mockLeaderBoardService.forceRefreshMaterializedViews.mockRejectedValue(
        serviceError,
      );

      await expect(controller.forceRefreshLeaderBoardScore()).rejects.toThrow(
        serviceError,
      );

      expect(
        mockLeaderBoardService.forceRefreshMaterializedViews,
      ).toHaveBeenCalled();
    });
  });

  describe('getLeaderBoardWeb', () => {
    it('should get web leaderboard successfully with all filters', async () => {
      const query = {
        period: PeriodEnumType.WEEKLY,
        page: 1,
        limit: 10,
        search: 'John',
        degree: 'Computer Science',
        institutionId: 'inst-123',
        program: 'BSc',
        level: '3',
      };

      const mockWebLeaderboardData = {
        data: [
          {
            student_id: 'student-123',
            first_name: 'John',
            last_name: 'Doe',
            total_score: 150,
            rank: 1,
            degree: 'Computer Science',
            institution_name: 'Test University',
          },
        ],
        total: 1,
        currentUser: {
          student_id: 'student-123',
          rank: 1,
        },
        userPosition: 1,
      };

      mockLeaderBoardService.getLeaderBoardFilterPeriodWeb.mockResolvedValue(
        mockWebLeaderboardData,
      );

      const result = await controller.getLeaderBoardWeb(query, mockUser);

      expect(
        mockLeaderBoardService.getLeaderBoardFilterPeriodWeb,
      ).toHaveBeenCalledWith(
        PeriodEnumType.WEEKLY,
        1,
        10,
        'John',
        '3',
        'inst-123',
        'Computer Science',
        'BSc',
        'user-123',
      );
      expect(result).toEqual(mockWebLeaderboardData);
    });

    it('should get web leaderboard without filters', async () => {
      const query = {
        period: PeriodEnumType.MONTHLY,
        page: 2,
        limit: 20,
      };

      const mockWebLeaderboardData = {
        data: [],
        total: 0,
        currentUser: null,
        userPosition: null,
      };

      mockLeaderBoardService.getLeaderBoardFilterPeriodWeb.mockResolvedValue(
        mockWebLeaderboardData,
      );

      const result = await controller.getLeaderBoardWeb(query, mockUser);

      expect(
        mockLeaderBoardService.getLeaderBoardFilterPeriodWeb,
      ).toHaveBeenCalledWith(
        PeriodEnumType.MONTHLY,
        2,
        20,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        'user-123',
      );
      expect(result).toEqual(mockWebLeaderboardData);
    });

    it('should propagate service errors for web leaderboard', async () => {
      const query = {
        period: PeriodEnumType.ALL_TIME,
        page: 1,
        limit: 10,
      };

      const serviceError = new Error('Web leaderboard failed');
      mockLeaderBoardService.getLeaderBoardFilterPeriodWeb.mockRejectedValue(
        serviceError,
      );

      await expect(
        controller.getLeaderBoardWeb(query, mockUser),
      ).rejects.toThrow(serviceError);

      expect(
        mockLeaderBoardService.getLeaderBoardFilterPeriodWeb,
      ).toHaveBeenCalled();
    });

    it('should handle partial filters', async () => {
      const query = {
        period: PeriodEnumType.QUARTERLY,
        page: 1,
        limit: 15,
        degree: 'Engineering',
        search: 'Jane',
      };

      const mockWebLeaderboardData = {
        data: [],
        total: 0,
        currentUser: null,
        userPosition: null,
      };

      mockLeaderBoardService.getLeaderBoardFilterPeriodWeb.mockResolvedValue(
        mockWebLeaderboardData,
      );

      await controller.getLeaderBoardWeb(query, mockUser);

      expect(
        mockLeaderBoardService.getLeaderBoardFilterPeriodWeb,
      ).toHaveBeenCalledWith(
        PeriodEnumType.QUARTERLY,
        1,
        15,
        'Jane',
        undefined,
        undefined,
        'Engineering',
        undefined,
        'user-123',
      );
    });
  });
});
