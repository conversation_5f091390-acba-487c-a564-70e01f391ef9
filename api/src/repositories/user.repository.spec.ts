import { Test, TestingModule } from '@nestjs/testing';
import { UserRepository } from './user.repository';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { users } from '@/db/schema';
import { normalizeEmail } from '../util/normalize-email';

// Mock the normalize-email utility
jest.mock('../util/normalize-email');

describe('UserRepository', () => {
  let repository: UserRepository;
  let mockDrizzleService: jest.Mocked<DrizzleService>;

  const mockUser = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    state: 'active',
    deleted: false,
    role: 'student',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    // Create mock DrizzleService
    mockDrizzleService = {
      db: {
        query: {
          users: {
            findFirst: jest.fn(),
          },
        },
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn(),
      },
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserRepository,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
      ],
    }).compile();

    repository = module.get<UserRepository>(UserRepository);

    // Setup normalize email mock
    (normalizeEmail as jest.Mock).mockImplementation((email: string) =>
      email ? email.toLowerCase().trim() : email,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserByKey', () => {
    it('should be defined', () => {
      expect(repository).toBeDefined();
    });

    it('should get user by email with normalization', async () => {
      const email = '<EMAIL>';

      mockDrizzleService.db.query.users.findFirst.mockResolvedValue(mockUser);

      const result = await repository.getUserByKey('email', email);

      expect(normalizeEmail).toHaveBeenCalledWith(email);
      expect(mockDrizzleService.db.query.users.findFirst).toHaveBeenCalledWith({
        where: expect.any(Object),
      });
      expect(result).toEqual(mockUser);
    });

    it('should get user by id without normalization', async () => {
      const userId = '123e4567-e89b-12d3-a456-426614174000';

      mockDrizzleService.db.query.users.findFirst.mockResolvedValue(mockUser);

      const result = await repository.getUserByKey('id', userId);

      expect(normalizeEmail).not.toHaveBeenCalled();
      expect(mockDrizzleService.db.query.users.findFirst).toHaveBeenCalledWith({
        where: expect.any(Object),
      });
      expect(result).toEqual(mockUser);
    });

    it('should return undefined when user not found', async () => {
      mockDrizzleService.db.query.users.findFirst.mockResolvedValue(undefined);

      const result = await repository.getUserByKey(
        'email',
        '<EMAIL>',
      );

      expect(result).toBeUndefined();
    });

    it('should handle other user keys', async () => {
      mockDrizzleService.db.query.users.findFirst.mockResolvedValue(mockUser);

      const result = await repository.getUserByKey('firstName', 'John');

      expect(result).toEqual(mockUser);
      expect(normalizeEmail).not.toHaveBeenCalled();
    });
  });

  describe('getUserByEmail', () => {
    it('should get user by email with case-insensitive matching', async () => {
      const email = '<EMAIL>';

      mockDrizzleService.db.query.users.findFirst.mockResolvedValue(mockUser);

      const result = await repository.getUserByEmail(email);

      expect(normalizeEmail).toHaveBeenCalledWith(email);
      expect(mockDrizzleService.db.query.users.findFirst).toHaveBeenCalledWith({
        where: expect.any(Object),
      });
      expect(result).toEqual(mockUser);
    });

    it('should return undefined when user not found by email', async () => {
      mockDrizzleService.db.query.users.findFirst.mockResolvedValue(undefined);

      const result = await repository.getUserByEmail('<EMAIL>');

      expect(result).toBeUndefined();
    });

    it('should handle empty email', async () => {
      const result = await repository.getUserByEmail('');

      expect(normalizeEmail).toHaveBeenCalledWith('');
    });
  });

  describe('validateActiveUser', () => {
    const userId = '123e4567-e89b-12d3-a456-426614174000';

    beforeEach(() => {
      // Reset the mock to return the select chain
      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn(),
        }),
      });
    });

    it('should return user data for valid active user', async () => {
      const userData = { state: 'active', deleted: false };

      mockDrizzleService.db.select().from().where.mockResolvedValue([userData]);

      const result = await repository.validateActiveUser(userId);

      expect(mockDrizzleService.db.select).toHaveBeenCalledWith({
        state: users.state,
        deleted: users.deleted,
      });
      expect(result).toEqual(userData);
    });

    it('should return null when user not found', async () => {
      mockDrizzleService.db.select().from().where.mockResolvedValue([]);

      const result = await repository.validateActiveUser(userId);

      expect(result).toBeNull();
    });

    it('should return null when user is not active', async () => {
      const userData = { state: 'inactive', deleted: false };

      mockDrizzleService.db.select().from().where.mockResolvedValue([userData]);

      const result = await repository.validateActiveUser(userId);

      expect(result).toBeNull();
    });

    it('should return null when user is deleted', async () => {
      const userData = { state: 'active', deleted: true };

      mockDrizzleService.db.select().from().where.mockResolvedValue([userData]);

      const result = await repository.validateActiveUser(userId);

      expect(result).toBeNull();
    });

    it('should return null when user is both inactive and deleted', async () => {
      const userData = { state: 'inactive', deleted: true };

      mockDrizzleService.db.select().from().where.mockResolvedValue([userData]);

      const result = await repository.validateActiveUser(userId);

      expect(result).toBeNull();
    });

    it('should handle database errors gracefully', async () => {
      const error = new Error('Database connection failed');
      mockDrizzleService.db.select().from().where.mockRejectedValue(error);

      const result = await repository.validateActiveUser(userId);

      expect(result).toBeNull();
    });

    it('should handle different user states', async () => {
      const testCases = [
        { state: 'pending', deleted: false, expected: null },
        { state: 'suspended', deleted: false, expected: null },
        {
          state: 'active',
          deleted: false,
          expected: { state: 'active', deleted: false },
        },
      ];

      for (const testCase of testCases) {
        mockDrizzleService.db
          .select()
          .from()
          .where.mockResolvedValue([
            {
              state: testCase.state,
              deleted: testCase.deleted,
            },
          ]);

        const result = await repository.validateActiveUser(userId);

        expect(result).toEqual(testCase.expected);
      }
    });
  });
});
