import { Test, TestingModule } from '@nestjs/testing';
import { PostRepository } from './post.repository';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { posts, events, opportunity } from '@/db/schema/posts';

describe('PostRepository', () => {
  let repository: PostRepository;
  let mockDrizzleService: jest.Mocked<DrizzleService>;

  const mockPost = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    title: 'Test Post',
    description: 'Test Description',
    imageUrl: 'https://example.com/image.jpg',
    status: 'active',
    disabled: false,
    type: 'general',
    postedBy: '456e7890-e89b-12d3-a456-426614174001',
    isGlobal: false,
    notify_users: false,
    club_id: null,
    scheduledAt: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  const mockEvent = {
    id: '789e0123-e89b-12d3-a456-426614174002',
    startDate: '2024-02-01T00:00:00Z',
    startTime: '10:00:00',
    endDate: '2024-02-01T00:00:00Z',
    endTime: '12:00:00',
    virtualLink: 'https://example.com/meeting',
    postId: '123e4567-e89b-12d3-a456-426614174000',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  const mockOpportunity = {
    id: '012e3456-e89b-12d3-a456-426614174003',
    eligibility: [1, 2, 3],
    applicationUrl: 'https://example.com/apply',
    startDate: '2024-02-01T00:00:00Z',
    startTime: '09:00:00',
    endDate: '2024-03-01T00:00:00Z',
    endTime: '17:00:00',
    postId: '123e4567-e89b-12d3-a456-426614174000',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  beforeEach(async () => {
    // Create mock DrizzleService
    mockDrizzleService = {
      db: {
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn(),
      },
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostRepository,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
      ],
    }).compile();

    repository = module.get<PostRepository>(PostRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPost', () => {
    it('should be defined', () => {
      expect(repository).toBeDefined();
    });

    it('should create a post successfully', async () => {
      const postData = {
        title: 'New Post',
        description: 'New Description',
        postedBy: '456e7890-e89b-12d3-a456-426614174001',
      };

      mockDrizzleService.db.returning.mockResolvedValue([mockPost]);

      const result = await repository.createPost(postData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalledWith(posts);
      expect(mockDrizzleService.db.values).toHaveBeenCalledWith(postData);
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual([mockPost]);
    });

    it('should create a post with custom transaction', async () => {
      const postData = {
        title: 'New Post',
        description: 'New Description',
        postedBy: '456e7890-e89b-12d3-a456-426614174001',
      };

      const mockTx = {
        insert: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockPost]),
      };

      const result = await repository.createPost(postData, mockTx as any);

      expect(mockTx.insert).toHaveBeenCalledWith(posts);
      expect(mockTx.values).toHaveBeenCalledWith(postData);
      expect(mockTx.returning).toHaveBeenCalled();
      expect(result).toEqual([mockPost]);
    });

    it('should handle post creation with all optional fields', async () => {
      const fullPostData = {
        title: 'Complete Post',
        description: 'Complete Description',
        imageUrl: 'https://example.com/image.jpg',
        status: 'draft' as const,
        disabled: true,
        type: 'event' as const,
        postedBy: '456e7890-e89b-12d3-a456-426614174001',
        isGlobal: true,
        notify_users: true,
        club_id: '789e0123-e89b-12d3-a456-426614174002',
        scheduledAt: '2024-02-01T10:00:00Z',
      };

      mockDrizzleService.db.returning.mockResolvedValue([mockPost]);

      const result = await repository.createPost(fullPostData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalledWith(posts);
      expect(mockDrizzleService.db.values).toHaveBeenCalledWith(fullPostData);
      expect(result).toEqual([mockPost]);
    });
  });

  describe('updatePost', () => {
    const postId = '123e4567-e89b-12d3-a456-426614174000';

    it('should update a post successfully', async () => {
      const updateData = {
        title: 'Updated Title',
        description: 'Updated Description',
      };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockPost, ...updateData },
      ]);

      const result = await repository.updatePost(updateData, postId);

      expect(mockDrizzleService.db.update).toHaveBeenCalledWith(posts);
      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(updateData);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual([{ ...mockPost, ...updateData }]);
    });

    it('should update post with custom transaction', async () => {
      const updateData = { title: 'Updated Title' };
      const mockTx = {
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest
          .fn()
          .mockResolvedValue([{ ...mockPost, ...updateData }]),
      };

      const result = await repository.updatePost(
        updateData,
        postId,
        mockTx as any,
      );

      expect(mockTx.update).toHaveBeenCalledWith(posts);
      expect(mockTx.set).toHaveBeenCalledWith(updateData);
      expect(result).toEqual([{ ...mockPost, ...updateData }]);
    });

    it('should handle partial updates', async () => {
      const updateData = { status: 'expired' as const };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockPost, ...updateData },
      ]);

      const result = await repository.updatePost(updateData, postId);

      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(updateData);
      expect(result).toEqual([{ ...mockPost, ...updateData }]);
    });
  });

  describe('updateEvent', () => {
    const eventId = '789e0123-e89b-12d3-a456-426614174002';

    it('should update an event successfully', async () => {
      const updateData = {
        startDate: '2024-03-01T00:00:00Z',
        virtualLink: 'https://updated-link.com',
      };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockEvent, ...updateData },
      ]);

      const result = await repository.updateEvent(updateData, eventId);

      expect(mockDrizzleService.db.update).toHaveBeenCalledWith(events);
      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(updateData);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual([{ ...mockEvent, ...updateData }]);
    });

    it('should update event with custom transaction', async () => {
      const updateData = { virtualLink: 'https://new-link.com' };
      const mockTx = {
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest
          .fn()
          .mockResolvedValue([{ ...mockEvent, ...updateData }]),
      };

      const result = await repository.updateEvent(
        updateData,
        eventId,
        mockTx as any,
      );

      expect(mockTx.update).toHaveBeenCalledWith(events);
      expect(result).toEqual([{ ...mockEvent, ...updateData }]);
    });

    it('should handle event time updates', async () => {
      const updateData = {
        startTime: '14:00:00',
        endTime: '16:00:00',
      };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockEvent, ...updateData },
      ]);

      const result = await repository.updateEvent(updateData, eventId);

      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(updateData);
      expect(result).toEqual([{ ...mockEvent, ...updateData }]);
    });
  });

  describe('updateOpportunity', () => {
    const opportunityId = '012e3456-e89b-12d3-a456-426614174003';

    it('should update an opportunity successfully', async () => {
      const updateData = {
        applicationUrl: 'https://updated-application.com',
        eligibility: [1, 2, 3, 4],
      };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockOpportunity, ...updateData },
      ]);

      const result = await repository.updateOpportunity(
        updateData,
        opportunityId,
      );

      // This test will fail due to the bug in the repository - it should update 'opportunity' table, not 'events'
      expect(mockDrizzleService.db.update).toHaveBeenCalledWith(opportunity);
      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(updateData);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.returning).toHaveBeenCalled();
      expect(result).toEqual([{ ...mockOpportunity, ...updateData }]);
    });

    it('should update opportunity with custom transaction', async () => {
      const updateData = { applicationUrl: 'https://new-application.com' };
      const mockTx = {
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest
          .fn()
          .mockResolvedValue([{ ...mockOpportunity, ...updateData }]),
      };

      const result = await repository.updateOpportunity(
        updateData,
        opportunityId,
        mockTx as any,
      );

      expect(mockTx.update).toHaveBeenCalledWith(opportunity);
      expect(result).toEqual([{ ...mockOpportunity, ...updateData }]);
    });

    it('should handle opportunity date updates', async () => {
      const updateData = {
        startDate: '2024-04-01T00:00:00Z',
        endDate: '2024-05-01T00:00:00Z',
      };

      mockDrizzleService.db.returning.mockResolvedValue([
        { ...mockOpportunity, ...updateData },
      ]);

      const result = await repository.updateOpportunity(
        updateData,
        opportunityId,
      );

      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(updateData);
      expect(result).toEqual([{ ...mockOpportunity, ...updateData }]);
    });
  });
});
