import { events, insertPostInput, opportunity, posts } from '@/db/schema/posts';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { Injectable } from '@nestjs/common';
import { eq } from 'drizzle-orm';

type EventInput = typeof events.$inferInsert;
type OpportunityInput = typeof opportunity.$inferInsert;

@Injectable()
export class PostRepository {
  constructor(private drizzle: DrizzleService) {}

  async createPost(data: insertPostInput, tx = this.drizzle.db) {
    return tx.insert(posts).values(data).returning();
  }

  async updatePost(
    data: Partial<insertPostInput>,
    id: string,
    tx = this.drizzle.db,
  ) {
    return tx.update(posts).set(data).where(eq(posts.id, id)).returning();
  }

  async updateEvent(
    data: Partial<EventInput>,
    id: string,
    tx = this.drizzle.db,
  ) {
    return tx.update(events).set(data).where(eq(events.id, id)).returning();
  }

  async updateOpportunity(
    data: Partial<OpportunityInput>,
    id: string,
    tx = this.drizzle.db,
  ) {
    return tx
      .update(opportunity)
      .set(data)
      .where(eq(opportunity.id, id))
      .returning();
  }
}
