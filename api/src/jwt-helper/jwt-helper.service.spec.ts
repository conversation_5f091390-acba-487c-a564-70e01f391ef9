import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { Test } from '@nestjs/testing';
import { JwtHelperService } from './jwt-helper.service';
import cryptoHelper from './helpers/crypto.helper';
import jsonwebtoken from 'jsonwebtoken';
import { BadRequestException } from '@nestjs/common';
import crypto from 'node:crypto';

jest.mock('jsonwebtoken', () => ({
  ...jest.requireActual('jsonwebtoken'),
  verify: jest.fn(),
  sign: jest.fn().mockReturnValue('token'),
}));

jest.mock('node:crypto', () => ({
  ...jest.requireActual('node:crypto'),
  randomInt: jest.fn(),
  createHmac: jest.fn(),
}));

describe('JwtHelperService', () => {
  let jwtHelperService: JwtHelperService;

  const mockEnvConfig = {
    ACCESS_TOKEN_SECRET: Buffer.from('access-secret'),
    REFRESH_TOKEN_SECRET: Buffer.from('refresh-secret'),
    ACCESS_TOKEN_EXPIRY: '15m',
    REFRESH_TOKEN_EXPIRY: '30d',
    BACKEND_URL: 'http://localhost:3000',
    FRONTEND_URL: 'http://localhost:3001',
    OTP_HASH_SECRET: 'otp-secret',
  };

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        JwtHelperService,
        {
          provide: EnvConfig,
          useValue: mockEnvConfig,
        },
      ],
    }).compile();

    jwtHelperService = moduleRef.get<JwtHelperService>(JwtHelperService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('getTokenFromHeader', () => {
    it('should extract token from Bearer authorization header', () => {
      const req = {
        headers: {
          authorization: 'Bearer valid-token',
        },
      } as any;

      const result = jwtHelperService.getTokenFromHeader(req);
      expect(result).toBe('valid-token');
    });

    it('should return null if no authorization header', () => {
      const req = {
        headers: {},
      } as any;

      const result = jwtHelperService.getTokenFromHeader(req);
      expect(result).toBeNull();
    });

    it('should return null if authorization header is not Bearer', () => {
      const req = {
        headers: {
          authorization: 'Basic token',
        },
      } as any;

      const result = jwtHelperService.getTokenFromHeader(req);
      expect(result).toBeNull();
    });

    it('should return null if no token after Bearer', () => {
      const req = {
        headers: {
          authorization: 'Bearer',
        },
      } as any;

      const result = jwtHelperService.getTokenFromHeader(req);
      expect(result).toBeNull();
    });
  });

  describe('generateAccessToken', () => {
    beforeEach(() => {
      cryptoHelper.secretFromBuffer = jest.fn().mockReturnValue('secret');
      cryptoHelper.asyncRandomBytes = jest
        .fn()
        .mockResolvedValue(Buffer.from('random-bytes'));
    });

    it('should generate access token with userId', async () => {
      const result = await jwtHelperService.generateAccessToken({
        userId: 'user-123',
      });

      expect(result).toBe('token');
      expect(jsonwebtoken.sign).toHaveBeenCalledWith(
        { userId: 'user-123', refreshTokenId: undefined },
        'secret',
        {
          jwtid: '72616e646f6d2d6279746573', // hex encoding of 'random-bytes'
          algorithm: 'HS256',
          expiresIn: '15m',
          issuer: 'http://localhost:3000',
          audience: ['http://localhost:3001'],
        },
      );
    });

    it('should generate access token with refreshTokenId', async () => {
      await jwtHelperService.generateAccessToken({
        userId: 'user-123',
        refreshTokenId: 'refresh-123',
      });

      expect(jsonwebtoken.sign).toHaveBeenCalledWith(
        { userId: 'user-123', refreshTokenId: 'refresh-123' },
        'secret',
        expect.objectContaining({
          jwtid: '72616e646f6d2d6279746573', // hex encoding of 'random-bytes'
          algorithm: 'HS256',
        }),
      );
    });

    it('should generate access token with custom expiresIn', async () => {
      await jwtHelperService.generateAccessToken({
        userId: 'user-123',
        expiresIn: '1h',
      });

      expect(jsonwebtoken.sign).toHaveBeenCalledWith(
        expect.any(Object),
        'secret',
        expect.objectContaining({
          expiresIn: '1h',
        }),
      );
    });
  });

  describe('generateRefreshToken', () => {
    beforeEach(() => {
      cryptoHelper.secretFromBuffer = jest
        .fn()
        .mockReturnValue('refresh-secret');
      cryptoHelper.asyncRandomBytes = jest
        .fn()
        .mockResolvedValue(Buffer.from('refresh-random'));
    });

    it('should generate refresh token', async () => {
      const result = await jwtHelperService.generateRefreshToken('user-123');

      expect(result).toEqual({
        token: 'token',
        jwtId: '726566726573682d72616e646f6d', // hex encoding of 'refresh-random'
      });
      expect(jsonwebtoken.sign).toHaveBeenCalledWith(
        { userId: 'user-123' },
        'refresh-secret',
        {
          jwtid: '726566726573682d72616e646f6d', // hex encoding of 'refresh-random'
          algorithm: 'HS256',
          expiresIn: '30d',
          issuer: 'http://localhost:3000',
          audience: ['http://localhost:3001'],
        },
      );
    });
  });

  describe('generateAuthTokens', () => {
    beforeEach(() => {
      cryptoHelper.secretFromBuffer = jest.fn().mockReturnValue('secret');
      cryptoHelper.asyncRandomBytes = jest
        .fn()
        .mockResolvedValue(Buffer.from('random'));
      (jsonwebtoken.sign as jest.Mock)
        .mockReturnValueOnce('refresh-token')
        .mockReturnValueOnce('access-token');
    });

    it('should generate both access and refresh tokens', async () => {
      const result = await jwtHelperService.generateAuthTokens('user-123');

      expect(result).toEqual({
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
      });
      expect(jsonwebtoken.sign).toHaveBeenCalledTimes(2);
    });
  });

  describe('verifyAccessToken', () => {
    beforeEach(() => {
      cryptoHelper.secretFromBuffer = jest
        .fn()
        .mockReturnValue('access-secret');
    });

    it('should verify valid access token', async () => {
      const mockPayload = { userId: 'user-123', jti: 'jwt-id' };
      (jsonwebtoken.verify as jest.Mock).mockReturnValue(mockPayload);

      const result = await jwtHelperService.verifyAccessToken({
        token: 'valid-token',
      });

      expect(result).toEqual(mockPayload);
      expect(jsonwebtoken.verify).toHaveBeenCalledWith(
        'valid-token',
        'access-secret',
        {
          ignoreExpiration: undefined,
          algorithms: ['HS256'],
          issuer: 'http://localhost:3000',
          audience: ['http://localhost:3001'],
        },
      );
    });

    it('should verify token with ignoreExpiration option', async () => {
      const mockPayload = { userId: 'user-123' };
      (jsonwebtoken.verify as jest.Mock).mockReturnValue(mockPayload);

      await jwtHelperService.verifyAccessToken({
        token: 'expired-token',
        ignoreExpiration: true,
      });

      expect(jsonwebtoken.verify).toHaveBeenCalledWith(
        'expired-token',
        'access-secret',
        expect.objectContaining({
          ignoreExpiration: true,
        }),
      );
    });

    it('should throw error for invalid token', async () => {
      (jsonwebtoken.verify as jest.Mock).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await expect(
        jwtHelperService.verifyAccessToken({ token: 'invalid-token' }),
      ).rejects.toThrow('Invalid token');
    });
  });

  describe('verifyRefreshToken', () => {
    beforeEach(() => {
      cryptoHelper.secretFromBuffer = jest
        .fn()
        .mockReturnValue('refresh-secret');
    });

    it('should verify valid refresh token', async () => {
      const mockPayload = { userId: 'user-123', jti: 'refresh-jwt-id' };
      (jsonwebtoken.verify as jest.Mock).mockReturnValue(mockPayload);

      const result = await jwtHelperService.verifyRefreshToken({
        token: 'valid-refresh-token',
      });

      expect(result).toEqual(mockPayload);
      expect(jsonwebtoken.verify).toHaveBeenCalledWith(
        'valid-refresh-token',
        'refresh-secret',
        {
          ignoreExpiration: undefined,
          algorithms: ['HS256'],
          issuer: 'http://localhost:3000',
          audience: ['http://localhost:3001'],
        },
      );
    });

    it('should throw error for invalid refresh token', async () => {
      (jsonwebtoken.verify as jest.Mock).mockImplementation(() => {
        throw new Error('Invalid refresh token');
      });

      await expect(
        jwtHelperService.verifyRefreshToken({ token: 'invalid-refresh-token' }),
      ).rejects.toThrow('Invalid refresh token');
    });
  });

  describe('refreshAccessToken', () => {
    const mockRequest = {
      headers: {
        authorization: 'Bearer access-token',
      },
    } as any;

    beforeEach(() => {
      cryptoHelper.secretFromBuffer = jest.fn().mockReturnValue('secret');
      cryptoHelper.asyncRandomBytes = jest
        .fn()
        .mockResolvedValue(Buffer.from('new-random'));
    });

    it('should refresh access token successfully', async () => {
      const refreshPayload = { userId: 'user-123', jti: 'refresh-jwt-id' };
      const accessPayload = {
        userId: 'user-123',
        refreshTokenId: 'refresh-jwt-id',
      };

      (jsonwebtoken.verify as jest.Mock)
        .mockReturnValueOnce(refreshPayload) // verifyRefreshToken
        .mockReturnValueOnce(accessPayload); // verifyAccessToken

      const result = await jwtHelperService.refreshAccessToken(
        mockRequest,
        'valid-refresh-token',
      );

      expect(result).toBe('token');
      expect(jsonwebtoken.verify).toHaveBeenCalledTimes(2);
    });

    it('should throw BadRequestException if refresh token is missing', async () => {
      await expect(
        jwtHelperService.refreshAccessToken(mockRequest, ''),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if access token is missing', async () => {
      const reqWithoutAuth = { headers: {} } as any;

      await expect(
        jwtHelperService.refreshAccessToken(reqWithoutAuth, 'refresh-token'),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if refresh token is invalid', async () => {
      (jsonwebtoken.verify as jest.Mock).mockReturnValueOnce(null);

      await expect(
        jwtHelperService.refreshAccessToken(
          mockRequest,
          'invalid-refresh-token',
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if access token is invalid', async () => {
      const refreshPayload = { userId: 'user-123', jti: 'refresh-jwt-id' };

      (jsonwebtoken.verify as jest.Mock)
        .mockReturnValueOnce(refreshPayload) // verifyRefreshToken
        .mockReturnValueOnce(null); // verifyAccessToken

      await expect(
        jwtHelperService.refreshAccessToken(mockRequest, 'valid-refresh-token'),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if refresh token IDs do not match', async () => {
      const refreshPayload = { userId: 'user-123', jti: 'refresh-jwt-id' };
      const accessPayload = {
        userId: 'user-123',
        refreshTokenId: 'different-jwt-id',
      };

      (jsonwebtoken.verify as jest.Mock)
        .mockReturnValueOnce(refreshPayload) // verifyRefreshToken
        .mockReturnValueOnce(accessPayload); // verifyAccessToken

      await expect(
        jwtHelperService.refreshAccessToken(mockRequest, 'valid-refresh-token'),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('OTP methods', () => {
    describe('generateOtp', () => {
      beforeEach(() => {
        (crypto.randomInt as jest.Mock).mockImplementation(() => {
          // Return predictable values for testing
          const values = [1, 2, 3, 4, 5, 6];
          return values[Math.floor(Math.random() * values.length)];
        });

        const mockHmac = {
          update: jest.fn().mockReturnThis(),
          digest: jest.fn().mockReturnValue('mocked-hash'),
        };
        (crypto.createHmac as jest.Mock).mockReturnValue(mockHmac);
      });

      it('should generate 6-digit OTP with hash', () => {
        const result = jwtHelperService.generateOtp();

        expect(result).toHaveProperty('otp');
        expect(result).toHaveProperty('otpHash');
        expect(result.otp).toMatch(/^\d{6}$/);
        expect(result.otpHash).toBe('mocked-hash');
      });
    });

    describe('hashOtp', () => {
      beforeEach(() => {
        const mockHmac = {
          update: jest.fn().mockReturnThis(),
          digest: jest.fn().mockReturnValue('hashed-otp'),
        };
        (crypto.createHmac as jest.Mock).mockReturnValue(mockHmac);
      });

      it('should hash OTP using HMAC SHA256', () => {
        const result = jwtHelperService.hashOtp('123456');

        expect(result).toBe('hashed-otp');
        expect(crypto.createHmac).toHaveBeenCalledWith('sha256', 'otp-secret');
      });
    });

    describe('verifyOtp', () => {
      beforeEach(() => {
        const mockHmac = {
          update: jest.fn().mockReturnThis(),
          digest: jest.fn().mockReturnValue('correct-hash'),
        };
        (crypto.createHmac as jest.Mock).mockReturnValue(mockHmac);
      });

      it('should return true for matching OTP', () => {
        const result = jwtHelperService.verifyOtp('123456', 'correct-hash');
        expect(result).toBe(true);
      });

      it('should return false for non-matching OTP', () => {
        const mockHmac = {
          update: jest.fn().mockReturnThis(),
          digest: jest.fn().mockReturnValue('wrong-hash'),
        };
        (crypto.createHmac as jest.Mock).mockReturnValue(mockHmac);

        const result = jwtHelperService.verifyOtp('123456', 'correct-hash');
        expect(result).toBe(false);
      });
    });
  });

  describe('getCookieOptions', () => {
    it('should return cookie options with default expiry', () => {
      const result = jwtHelperService.getCookieOptions();

      expect(result).toEqual({
        httpOnly: true,
        secure: true,
        signed: false,
        expires: expect.any(Date),
        sameSite: 'none',
      });
    });

    it('should use custom refresh token expiry if provided', () => {
      // Note: This test would need to be restructured to properly test custom expiry
      // For now, we'll test the default behavior
      const result = jwtHelperService.getCookieOptions();
      expect(result.expires).toBeInstanceOf(Date);
    });
  });
});
