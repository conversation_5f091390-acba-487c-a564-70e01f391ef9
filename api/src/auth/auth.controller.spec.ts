import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { BadRequestException } from '@nestjs/common';
import { Request, Response } from 'express';
import { AppClients } from '@app/shared/constants/auth.constants';

// Mock the decorators and guards
jest.mock('@/guards/role.guard', () => ({
  RoleGuard: jest.fn().mockImplementation(() => ({
    canActivate: jest.fn().mockReturnValue(true),
  })),
}));

jest.mock('nest-access-control', () => ({
  UseRoles: () => jest.fn(),
}));

jest.mock('@/guards/request-validation.decorator', () => ({
  CLIENT_TYPE: () => jest.fn(),
}));

jest.mock('@/guards/user.decorator', () => ({
  User: () => jest.fn(),
}));

jest.mock('@/common/pipes/custom-parse-uuid', () => ({
  CustomParseUUIDPipe: jest.fn().mockImplementation(() => ({
    transform: jest.fn((value) => value),
  })),
}));

jest.mock('@nestjs/swagger', () => ({
  ApiOperation: () => jest.fn(),
  ApiOkResponse: () => jest.fn(),
  ApiBadRequestResponse: () => jest.fn(),
  ApiUnauthorizedResponse: () => jest.fn(),
  ApiForbiddenResponse: () => jest.fn(),
  ApiNotFoundResponse: () => jest.fn(),
  ApiBearerAuth: () => jest.fn(),
  ApiParam: () => jest.fn(),
  ApiBody: () => jest.fn(),
  ApiTags: () => jest.fn(),
  ApiProperty: () => jest.fn(),
  ApiHeader: () => jest.fn(),
  ApiQuery: () => jest.fn(),
}));

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  const mockAuthService = {
    login: jest.fn(),
    verifyOtp: jest.fn(),
    verifyMagicLink: jest.fn(),
    refreshAccessToken: jest.fn(),
    deactivateAccount: jest.fn(),
    approveWaitingList: jest.fn(),
    getWaitingList: jest.fn(),
    disApproveWaitingList: jest.fn(),
    notifyPendingNonAcademicUsers: jest.fn(),
    deleteOwnAccount: jest.fn(),
    deleteUser: jest.fn(),
    bulkDeleteUsers: jest.fn(),
  };

  const mockRequest = {
    headers: { 'x-client-type': AppClients.MOBILE },
  } as Request;

  const mockResponse = {
    set: jest.fn(),
  } as unknown as Response;

  const mockUser = {
    id: '123e4567-e89b-12d3-a456-************',
    email: '<EMAIL>',
    role: 'student',
  };

  beforeEach(async () => {
    // Create controller directly to avoid dependency injection issues
    controller = new AuthController(mockAuthService as any);
    authService = mockAuthService as any;
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('login', () => {
    const loginDto = { email: '<EMAIL>' };

    it('should return OTP sent message for mobile client', async () => {
      mockAuthService.login.mockResolvedValue(undefined);
      const req = {
        ...mockRequest,
        headers: { 'x-client-type': AppClients.MOBILE },
      };

      const result = await controller.login(req as Request, loginDto);

      expect(authService.login).toHaveBeenCalledWith(loginDto, req);
      expect(result.message).toContain('OTP Sent');
    });

    it('should return magic link sent message for web client', async () => {
      mockAuthService.login.mockResolvedValue(undefined);
      const req = {
        ...mockRequest,
        headers: { 'x-client-type': AppClients.WEB },
      };

      const result = await controller.login(req as Request, loginDto);

      expect(authService.login).toHaveBeenCalledWith(loginDto, req);
      expect(result.message).toContain('Magic Link Sent');
    });

    it('should return service result when it contains statusCode', async () => {
      const serviceResult = { statusCode: 403, message: 'Access denied' };
      mockAuthService.login.mockResolvedValue(serviceResult);

      const result = await controller.login(mockRequest, loginDto);

      expect(result).toEqual(serviceResult);
    });

    it('should handle login errors', async () => {
      const error = new Error('Login failed');
      mockAuthService.login.mockRejectedValue(error);

      await expect(controller.login(mockRequest, loginDto)).rejects.toThrow(
        error,
      );
    });
  });

  describe('verifyOtp', () => {
    const otpDto = { otp: '123456' };
    const authResult = {
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      user: mockUser,
      cookieOptions: { httpOnly: true },
      deleted: false,
    };

    it('should verify OTP and set cookies', async () => {
      mockAuthService.verifyOtp.mockResolvedValue(authResult);

      const result = await controller.verifyOtp(mockResponse, otpDto);

      expect(authService.verifyOtp).toHaveBeenCalledWith(otpDto.otp);
      expect(mockResponse.set).toHaveBeenCalled();
      expect(result).toEqual({
        accessToken: authResult.accessToken,
        user: authResult.user,
        deleted: authResult.deleted,
      });
    });

    it('should handle OTP verification errors', async () => {
      const error = new Error('Invalid OTP');
      mockAuthService.verifyOtp.mockRejectedValue(error);

      await expect(controller.verifyOtp(mockResponse, otpDto)).rejects.toThrow(
        error,
      );
    });
  });

  describe('verifyMagicLink', () => {
    const magicLinkDto = { token: 'magic-token' };
    const authResult = {
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      user: mockUser,
      cookieOptions: { httpOnly: true },
    };

    it('should verify magic link and set cookies', async () => {
      mockAuthService.verifyMagicLink.mockResolvedValue(authResult);

      const result = await controller.verifyMagicLink(
        mockResponse,
        magicLinkDto,
      );

      expect(authService.verifyMagicLink).toHaveBeenCalledWith(
        magicLinkDto.token,
      );
      expect(mockResponse.set).toHaveBeenCalled();
      expect(result).toEqual({
        accessToken: authResult.accessToken,
        user: authResult.user,
      });
    });

    it('should handle magic link verification errors', async () => {
      const error = new Error('Invalid token');
      mockAuthService.verifyMagicLink.mockRejectedValue(error);

      await expect(
        controller.verifyMagicLink(mockResponse, magicLinkDto),
      ).rejects.toThrow(error);
    });
  });

  describe('refreshAccessToken', () => {
    const refreshTokenDto = { refreshToken: 'refresh-token' };

    it('should refresh access token', async () => {
      const newAccessToken = 'new-access-token';
      mockAuthService.refreshAccessToken.mockResolvedValue(newAccessToken);

      const result = await controller.refreshAccessToken(
        mockRequest,
        refreshTokenDto,
      );

      expect(authService.refreshAccessToken).toHaveBeenCalledWith(
        mockRequest,
        refreshTokenDto.refreshToken,
      );
      expect(result).toEqual({ accessToken: newAccessToken });
    });

    it('should handle refresh token errors', async () => {
      const error = new Error('Invalid refresh token');
      mockAuthService.refreshAccessToken.mockRejectedValue(error);

      await expect(
        controller.refreshAccessToken(mockRequest, refreshTokenDto),
      ).rejects.toThrow(error);
    });
  });

  describe('deactivate', () => {
    it('should deactivate user account', async () => {
      mockAuthService.deactivateAccount.mockResolvedValue(undefined);

      const result = await controller.deactivate(mockUser as any);

      expect(authService.deactivateAccount).toHaveBeenCalledWith(mockUser.id);
      expect(result.message).toBe('Account deactivated successfully');
    });

    it('should handle deactivation errors', async () => {
      const error = new Error('Deactivation failed');
      mockAuthService.deactivateAccount.mockRejectedValue(error);

      await expect(controller.deactivate(mockUser as any)).rejects.toThrow(
        error,
      );
    });
  });

  describe('approveUsers', () => {
    const approveDto = { userIds: ['user1', 'user2'] };

    it('should approve users successfully', async () => {
      const serviceResult = {
        total: 2,
        data: [
          { id: 'user1', email: '<EMAIL>' },
          { id: 'user2', email: '<EMAIL>' },
        ],
        message: 'Users approved',
      };
      mockAuthService.approveWaitingList.mockResolvedValue(serviceResult);

      const result = await controller.approveUsers(approveDto);

      expect(authService.approveWaitingList).toHaveBeenCalledWith(
        approveDto.userIds,
      );
      expect(result).toBeInstanceOf(Object);
    });

    it('should throw error when userIds is empty', async () => {
      await expect(controller.approveUsers({ userIds: [] })).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw error when userIds is undefined', async () => {
      await expect(controller.approveUsers({} as any)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('getAllUsers', () => {
    const queryParams = { page: 1, limit: 10 };

    it('should get waiting list users', async () => {
      const serviceResult = {
        data: [mockUser],
        pagination: { total: 1, page: 1, limit: 10 },
      };
      mockAuthService.getWaitingList.mockResolvedValue(serviceResult);

      const result = await controller.getAllUsers(queryParams as any);

      expect(authService.getWaitingList).toHaveBeenCalledWith(queryParams);
      expect(result).toEqual(serviceResult);
    });

    it('should handle get users errors', async () => {
      const error = new Error('Failed to get users');
      mockAuthService.getWaitingList.mockRejectedValue(error);

      await expect(controller.getAllUsers(queryParams as any)).rejects.toThrow(
        error,
      );
    });
  });

  describe('disApproveUsers', () => {
    const disapproveDto = { userIds: ['user1', 'user2'] };

    it('should disapprove users successfully', async () => {
      const serviceResult = {
        total: 2,
        data: [
          { id: 'user1', email: '<EMAIL>' },
          { id: 'user2', email: '<EMAIL>' },
        ],
        message: 'Users deleted',
      };
      mockAuthService.disApproveWaitingList.mockResolvedValue(serviceResult);

      const result = await controller.disApproveUsers(disapproveDto);

      expect(authService.disApproveWaitingList).toHaveBeenCalledWith(
        disapproveDto.userIds,
      );
      expect(result).toBeInstanceOf(Object);
    });

    it('should throw error when userIds is empty', async () => {
      await expect(controller.disApproveUsers({ userIds: [] })).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('notifyPendingUsers', () => {
    it('should notify pending users', async () => {
      const serviceResult = { count: 5, failures: 0, duration: '2s' };
      mockAuthService.notifyPendingNonAcademicUsers.mockResolvedValue(
        serviceResult,
      );

      const result = await controller.notifyPendingUsers();

      expect(authService.notifyPendingNonAcademicUsers).toHaveBeenCalled();
      expect(result.count).toBe(5);
      expect(result.message).toContain('5 pending users');
    });

    it('should handle notification errors', async () => {
      const error = new Error('Notification failed');
      mockAuthService.notifyPendingNonAcademicUsers.mockRejectedValue(error);

      await expect(controller.notifyPendingUsers()).rejects.toThrow(error);
    });
  });

  describe('deleteOwnAccount', () => {
    const deleteDto = { confirmationText: 'DELETE' };

    it('should delete own account', async () => {
      const serviceResult = {
        message: 'Account deleted successfully',
        deletedUser: { id: mockUser.id, email: mockUser.email },
      };
      mockAuthService.deleteOwnAccount.mockResolvedValue(serviceResult);

      const result = await controller.deleteOwnAccount(
        mockUser as any,
        deleteDto,
      );

      expect(authService.deleteOwnAccount).toHaveBeenCalledWith(
        mockUser.id,
        deleteDto.confirmationText,
      );
      expect(result.message).toBe(serviceResult.message);
      expect(result.deletedUser).toEqual(serviceResult.deletedUser);
    });

    it('should handle account deletion errors', async () => {
      const error = new Error('Deletion failed');
      mockAuthService.deleteOwnAccount.mockRejectedValue(error);

      await expect(
        controller.deleteOwnAccount(mockUser as any, deleteDto),
      ).rejects.toThrow(error);
    });
  });

  describe('deleteUser', () => {
    const targetUserId = '456e7890-e89b-12d3-a456-************';

    it('should delete user as admin', async () => {
      const serviceResult = {
        message: 'User deleted successfully',
        deletedUser: { id: targetUserId, email: '<EMAIL>' },
      };
      mockAuthService.deleteUser.mockResolvedValue(serviceResult);

      const result = await controller.deleteUser(targetUserId, mockUser as any);

      expect(authService.deleteUser).toHaveBeenCalledWith(
        targetUserId,
        mockUser.id,
      );
      expect(result).toEqual(serviceResult);
    });

    it('should prevent user from deleting themselves via admin endpoint', async () => {
      await expect(
        controller.deleteUser(mockUser.id, mockUser as any),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle user deletion errors', async () => {
      const error = new Error('User deletion failed');
      mockAuthService.deleteUser.mockRejectedValue(error);

      await expect(
        controller.deleteUser(targetUserId, mockUser as any),
      ).rejects.toThrow(error);
    });
  });

  describe('bulkDeleteUsers', () => {
    const bulkDeleteDto = { userIds: ['user1', 'user2', 'user3'] };

    it('should initiate bulk deletion', async () => {
      const serviceResult = {
        message: 'Bulk deletion initiated',
        jobId: 'job-123',
        usersQueued: 3,
      };
      mockAuthService.bulkDeleteUsers.mockResolvedValue(serviceResult);

      const result = await controller.bulkDeleteUsers(
        bulkDeleteDto,
        mockUser as any,
      );

      expect(authService.bulkDeleteUsers).toHaveBeenCalledWith(
        bulkDeleteDto.userIds,
        mockUser,
      );
      expect(result).toEqual({
        message: serviceResult.message,
        jobId: serviceResult.jobId,
        usersQueued: serviceResult.usersQueued,
      });
    });

    it('should handle bulk deletion errors', async () => {
      const error = new Error('Bulk deletion failed');
      mockAuthService.bulkDeleteUsers.mockRejectedValue(error);

      await expect(
        controller.bulkDeleteUsers(bulkDeleteDto, mockUser as any),
      ).rejects.toThrow(error);
    });
  });
});
