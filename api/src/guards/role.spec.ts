import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RoleGuard } from './role.guard';
import { AuthGuard, RolesGuard } from './auth.guard';
import { RolesBuilder } from 'nest-access-control';
import { createMock } from '@golevelup/ts-jest';
import { JwtHelperService } from '../jwt-helper/jwt-helper.service';
import { RepositoryService } from '../repositories/repository.service';
import { user_states, user_roles } from '@/db/schema';

describe('RoleGuard', () => {
  let roleGuard: RoleGuard;
  let reflector: Reflector;
  let rolesBuilder: RolesBuilder;

  beforeEach(() => {
    reflector = createMock<Reflector>();
    rolesBuilder = createMock<RolesBuilder>();
    roleGuard = new RoleGuard(reflector, rolesBuilder);
  });

  it('should return user roles', async () => {
    const mockContext = createMock<ExecutionContext>();
    const mockUser = { role: 'admin' };

    jest.spyOn(roleGuard as any, 'getUser').mockResolvedValue(mockUser);

    const roles = await (roleGuard as any).getUserRoles(mockContext);
    expect(roles).toBe('admin');
  });

  it('should return user roles as an array', async () => {
    const mockContext = createMock<ExecutionContext>();
    const mockUser = { role: ['admin', 'user'] };

    jest.spyOn(roleGuard as any, 'getUser').mockResolvedValue(mockUser);

    const roles = await (roleGuard as any).getUserRoles(mockContext);
    expect(roles).toEqual(['admin', 'user']);
  });
});

describe('AuthGuard', () => {
  let guard: AuthGuard;
  let jwtHelperService: JwtHelperService;
  let repositoryService: RepositoryService;
  let reflector: Reflector;

  beforeEach(() => {
    jwtHelperService = createMock<JwtHelperService>();
    repositoryService = createMock<RepositoryService>();
    reflector = createMock<Reflector>();
    guard = new AuthGuard(jwtHelperService, repositoryService, reflector);
  });

  const createMockExecutionContext = (headers: any = {}): ExecutionContext => {
    const mockRequest = {
      headers,
      user: undefined,
    };

    return {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
      getHandler: jest.fn(),
      getClass: jest.fn(),
    } as any;
  };

  describe('canActivate', () => {
    it('should allow access to public routes', async () => {
      const context = createMockExecutionContext();
      (reflector.getAllAndOverride as jest.Mock).mockReturnValue(true);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(reflector.getAllAndOverride).toHaveBeenCalledWith('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);
    });

    it('should authenticate user with valid token', async () => {
      const context = createMockExecutionContext({
        authorization: 'Bearer valid-token',
      });
      const mockUser = {
        id: 'user-123',
        role: user_roles.STUDENT,
        state: user_states.ACTIVE,
      };
      const mockPayload = { userId: 'user-123' };

      (reflector.getAllAndOverride as jest.Mock).mockReturnValue(false);
      (jwtHelperService.verifyAccessToken as jest.Mock).mockResolvedValue(
        mockPayload,
      );
      (repositoryService.getUserByKey as jest.Mock).mockResolvedValue(mockUser);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(jwtHelperService.verifyAccessToken).toHaveBeenCalledWith({
        token: 'valid-token',
      });
      expect(repositoryService.getUserByKey).toHaveBeenCalledWith(
        'id',
        'user-123',
      );
    });

    it('should authenticate user with verified state', async () => {
      const context = createMockExecutionContext({
        authorization: 'Bearer valid-token',
      });
      const mockUser = {
        id: 'user-123',
        role: user_roles.STUDENT,
        state: user_states.VERIFIED,
      };
      const mockPayload = { userId: 'user-123' };

      (reflector.getAllAndOverride as jest.Mock).mockReturnValue(false);
      (jwtHelperService.verifyAccessToken as jest.Mock).mockResolvedValue(
        mockPayload,
      );
      (repositoryService.getUserByKey as jest.Mock).mockResolvedValue(mockUser);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('should throw UnauthorizedException if no authorization header', async () => {
      const context = createMockExecutionContext();
      (reflector.getAllAndOverride as jest.Mock).mockReturnValue(false);

      await expect(guard.canActivate(context)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw UnauthorizedException if token verification fails', async () => {
      const context = createMockExecutionContext({
        authorization: 'Bearer invalid-token',
      });

      (reflector.getAllAndOverride as jest.Mock).mockReturnValue(false);
      (jwtHelperService.verifyAccessToken as jest.Mock).mockRejectedValue(
        new Error('Invalid token'),
      );

      await expect(guard.canActivate(context)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw UnauthorizedException if user not found', async () => {
      const context = createMockExecutionContext({
        authorization: 'Bearer valid-token',
      });
      const mockPayload = { userId: 'user-123' };

      (reflector.getAllAndOverride as jest.Mock).mockReturnValue(false);
      (jwtHelperService.verifyAccessToken as jest.Mock).mockResolvedValue(
        mockPayload,
      );
      (repositoryService.getUserByKey as jest.Mock).mockResolvedValue(null);

      await expect(guard.canActivate(context)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw UnauthorizedException if user is not active or verified', async () => {
      const context = createMockExecutionContext({
        authorization: 'Bearer valid-token',
      });
      const mockUser = {
        id: 'user-123',
        role: user_roles.STUDENT,
        state: user_states.PENDING,
      };
      const mockPayload = { userId: 'user-123' };

      (reflector.getAllAndOverride as jest.Mock).mockReturnValue(false);
      (jwtHelperService.verifyAccessToken as jest.Mock).mockResolvedValue(
        mockPayload,
      );
      (repositoryService.getUserByKey as jest.Mock).mockResolvedValue(mockUser);

      await expect(guard.canActivate(context)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });
});

describe('RolesGuard', () => {
  let guard: RolesGuard;
  let reflector: Reflector;

  beforeEach(() => {
    reflector = createMock<Reflector>();
    guard = new RolesGuard(reflector);
  });

  const createMockExecutionContext = (user: any = {}): ExecutionContext => {
    const mockRequest = { user };

    return {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
      getHandler: jest.fn(),
      getClass: jest.fn(),
    } as any;
  };

  describe('canActivate', () => {
    it('should allow access if no roles are required', async () => {
      const context = createMockExecutionContext();
      (reflector.getAllAndOverride as jest.Mock).mockReturnValue(null);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('should allow access if user has required role', async () => {
      const context = createMockExecutionContext({ role: user_roles.ADMIN });
      (reflector.getAllAndOverride as jest.Mock).mockReturnValue([
        user_roles.ADMIN,
        user_roles.STUDENT,
      ]);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('should deny access if user does not have required role', async () => {
      const context = createMockExecutionContext({ role: user_roles.STUDENT });
      (reflector.getAllAndOverride as jest.Mock).mockReturnValue([
        user_roles.ADMIN,
      ]);

      const result = await guard.canActivate(context);

      expect(result).toBe(false);
    });

    it('should allow access if user has one of multiple required roles', async () => {
      const context = createMockExecutionContext({ role: user_roles.STUDENT });
      (reflector.getAllAndOverride as jest.Mock).mockReturnValue([
        user_roles.ADMIN,
        user_roles.STUDENT,
      ]);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
    });
  });
});
